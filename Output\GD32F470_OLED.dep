Dependencies for Project 'GD32F470', Target 'OLED': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARM_Compiler_5.06u7
F (..\..\Drivers\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x684ED0B6)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

--pd "__UVISION_VERSION SETA 542" --pd "GD32F470 SETA 1"

--list ..\..\output\startup_gd32f450_470.lst --xref -o ..\..\output\startup_gd32f450_470.o --depend ..\..\output\startup_gd32f450_470.d)
F (..\..\User\main.c)(0x684ED0B1)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\main.o --omf_browse ..\..\output\main.crf --depend ..\..\output\main.d)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
I (..\..\Drivers\./BSP/LED/led.h)(0x684ED0B2)
I (..\..\Drivers\./BSP/OLED/oled.h)(0x684ED0B2)
I (..\..\Drivers\./BSP/KEY/key.h)(0x684ED0B3)
I (..\..\Drivers\./BSP/RTC/rtc.h)(0x684ED0B2)
I (..\..\Drivers\./BSP/ADC/adc.h)(0x684ED0B2)
I (..\..\Drivers\./BSP/SDIO/sd_conf.h)(0x684ED0B2)
I (..\..\Drivers\./BSP/SDIO/sdio_sdcard.h)(0x684ED0B2)
I (..\..\Drivers\./BSP/NORFLASH/norflash.h)(0x684ED0B2)
I (..\..\Middlewares\./FATFS/exfuns/exfuns.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ff.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ffconf.h)(0x684ED0B5)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\string.h)(0x5E8E3CC2)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\time.h)(0x5E8E3CC2)
I (..\..\Drivers\./BSP/TIMER/timer.h)(0x684ED0B3)
F (..\..\User\gd32f4xx_it.c)(0x684ED0B1)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_it.o --omf_browse ..\..\output\gd32f4xx_it.crf --depend ..\..\output\gd32f4xx_it.d)
I (..\..\User\gd32f4xx_it.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x684ED0B6)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\system_gd32f4xx.o --omf_browse ..\..\output\system_gd32f4xx.crf --depend ..\..\output\system_gd32f4xx.d)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\SYSTEM\delay\delay.c)(0x684ED0B5)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\delay.o --omf_browse ..\..\output\delay.crf --depend ..\..\output\delay.d)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
F (..\..\Drivers\SYSTEM\sys\sys.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\sys.o --omf_browse ..\..\output\sys.crf --depend ..\..\output\sys.d)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\SYSTEM\usart\usart.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\usart.o --omf_browse ..\..\output\usart.crf --depend ..\..\output\usart.d)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x684ED0B3)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_adc.o --omf_browse ..\..\output\gd32f4xx_adc.crf --depend ..\..\output\gd32f4xx_adc.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x684ED0B3)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_can.o --omf_browse ..\..\output\gd32f4xx_can.crf --depend ..\..\output\gd32f4xx_can.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x684ED0B3)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_crc.o --omf_browse ..\..\output\gd32f4xx_crc.crf --depend ..\..\output\gd32f4xx_crc.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x684ED0B3)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_ctc.o --omf_browse ..\..\output\gd32f4xx_ctc.crf --depend ..\..\output\gd32f4xx_ctc.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x684ED0B3)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_dac.o --omf_browse ..\..\output\gd32f4xx_dac.crf --depend ..\..\output\gd32f4xx_dac.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_dbg.o --omf_browse ..\..\output\gd32f4xx_dbg.crf --depend ..\..\output\gd32f4xx_dbg.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_dci.o --omf_browse ..\..\output\gd32f4xx_dci.crf --depend ..\..\output\gd32f4xx_dci.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_dma.o --omf_browse ..\..\output\gd32f4xx_dma.crf --depend ..\..\output\gd32f4xx_dma.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_enet.o --omf_browse ..\..\output\gd32f4xx_enet.crf --depend ..\..\output\gd32f4xx_enet.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x684ED0B5)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_exmc.o --omf_browse ..\..\output\gd32f4xx_exmc.crf --depend ..\..\output\gd32f4xx_exmc.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_exti.o --omf_browse ..\..\output\gd32f4xx_exti.crf --depend ..\..\output\gd32f4xx_exti.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_fmc.o --omf_browse ..\..\output\gd32f4xx_fmc.crf --depend ..\..\output\gd32f4xx_fmc.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_fwdgt.o --omf_browse ..\..\output\gd32f4xx_fwdgt.crf --depend ..\..\output\gd32f4xx_fwdgt.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_gpio.o --omf_browse ..\..\output\gd32f4xx_gpio.crf --depend ..\..\output\gd32f4xx_gpio.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_i2c.o --omf_browse ..\..\output\gd32f4xx_i2c.crf --depend ..\..\output\gd32f4xx_i2c.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_ipa.o --omf_browse ..\..\output\gd32f4xx_ipa.crf --depend ..\..\output\gd32f4xx_ipa.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_iref.o --omf_browse ..\..\output\gd32f4xx_iref.crf --depend ..\..\output\gd32f4xx_iref.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_misc.o --omf_browse ..\..\output\gd32f4xx_misc.crf --depend ..\..\output\gd32f4xx_misc.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_pmu.o --omf_browse ..\..\output\gd32f4xx_pmu.crf --depend ..\..\output\gd32f4xx_pmu.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_rcu.o --omf_browse ..\..\output\gd32f4xx_rcu.crf --depend ..\..\output\gd32f4xx_rcu.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_rtc.o --omf_browse ..\..\output\gd32f4xx_rtc.crf --depend ..\..\output\gd32f4xx_rtc.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_sdio.o --omf_browse ..\..\output\gd32f4xx_sdio.crf --depend ..\..\output\gd32f4xx_sdio.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_spi.o --omf_browse ..\..\output\gd32f4xx_spi.crf --depend ..\..\output\gd32f4xx_spi.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_syscfg.o --omf_browse ..\..\output\gd32f4xx_syscfg.crf --depend ..\..\output\gd32f4xx_syscfg.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_timer.o --omf_browse ..\..\output\gd32f4xx_timer.crf --depend ..\..\output\gd32f4xx_timer.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_tli.o --omf_browse ..\..\output\gd32f4xx_tli.crf --depend ..\..\output\gd32f4xx_tli.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_trng.o --omf_browse ..\..\output\gd32f4xx_trng.crf --depend ..\..\output\gd32f4xx_trng.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_usart.o --omf_browse ..\..\output\gd32f4xx_usart.crf --depend ..\..\output\gd32f4xx_usart.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x684ED0B4)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\gd32f4xx_wwdgt.o --omf_browse ..\..\output\gd32f4xx_wwdgt.crf --depend ..\..\output\gd32f4xx_wwdgt.d)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\BSP\LED\led.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\led.o --omf_browse ..\..\output\led.crf --depend ..\..\output\led.d)
I (..\..\Drivers\./BSP/LED/led.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\BSP\IIC\myiic.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\myiic.o --omf_browse ..\..\output\myiic.crf --depend ..\..\output\myiic.d)
I (..\..\Drivers\./BSP/IIC/myiic.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
F (..\..\Drivers\BSP\OLED\oled.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\oled.o --omf_browse ..\..\output\oled.crf --depend ..\..\output\oled.d)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\./BSP/OLED/oled.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./BSP/OLED/oledfont.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
I (..\..\Drivers\./BSP/IIC/myiic.h)(0x684ED0B2)
F (..\..\Drivers\BSP\KEY\key.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\key.o --omf_browse ..\..\output\key.crf --depend ..\..\output\key.d)
I (..\..\Drivers\./BSP/KEY/key.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
F (..\..\Drivers\BSP\ADC\adc.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\adc.o --omf_browse ..\..\output\adc.crf --depend ..\..\output\adc.d)
I (..\..\Drivers\./BSP/ADC/adc.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
F (..\..\Drivers\BSP\FMC\fmc.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\fmc.o --omf_browse ..\..\output\fmc.crf --depend ..\..\output\fmc.d)
I (..\..\Drivers\./BSP/FMC/fmc.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
F (..\..\Drivers\BSP\NORFLASH\norflash.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\norflash.o --omf_browse ..\..\output\norflash.crf --depend ..\..\output\norflash.d)
I (..\..\Drivers\./BSP/SPI/spi.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
I (..\..\Drivers\./BSP/NORFLASH/norflash.h)(0x684ED0B2)
F (..\..\Drivers\BSP\RTC\rtc.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\rtc.o --omf_browse ..\..\output\rtc.crf --depend ..\..\output\rtc.d)
I (..\..\Drivers\./BSP/RTC/rtc.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./BSP/LED/led.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
I (..\..\Drivers\./SYSTEM/delay/delay.h)(0x684ED0B4)
F (..\..\Drivers\BSP\SDIO\sd_conf.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\sd_conf.o --omf_browse ..\..\output\sd_conf.crf --depend ..\..\output\sd_conf.d)
I (..\..\Drivers\./BSP/SDIO/sd_conf.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./BSP/SDIO/sdio_sdcard.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
F (..\..\Drivers\BSP\SDIO\sdio_sdcard.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\sdio_sdcard.o --omf_browse ..\..\output\sdio_sdcard.crf --depend ..\..\output\sdio_sdcard.d)
I (..\..\Drivers\./BSP/SDIO/sdio_sdcard.h)(0x684ED0B2)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
F (..\..\Drivers\BSP\SPI\spi.c)(0x684ED0B2)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\spi.o --omf_browse ..\..\output\spi.crf --depend ..\..\output\spi.d)
I (..\..\Drivers\./BSP/SPI/spi.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\Drivers\BSP\TIMER\timer.c)(0x684ED0B3)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\timer.o --omf_browse ..\..\output\timer.crf --depend ..\..\output\timer.d)
I (..\..\Drivers\./BSP/TIMER/timer.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./BSP/LED/led.h)(0x684ED0B2)
F (..\..\Middlewares\FATFS\source\diskio.c)(0x684ED0B5)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\diskio.o --omf_browse ..\..\output\diskio.crf --depend ..\..\output\diskio.d)
I (..\..\Middlewares\./MALLOC/malloc.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
I (..\..\Middlewares\./FATFS/source/diskio.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ff.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ffconf.h)(0x684ED0B5)
I (..\..\Drivers\./BSP/SDIO/sd_conf.h)(0x684ED0B2)
I (..\..\Drivers\./BSP/SDIO/sdio_sdcard.h)(0x684ED0B2)
I (..\..\Drivers\./BSP/NORFLASH/norflash.h)(0x684ED0B2)
F (..\..\Middlewares\FATFS\source\ff.c)(0x684ED0B5)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\ff.o --omf_browse ..\..\output\ff.crf --depend ..\..\output\ff.d)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\string.h)(0x5E8E3CC2)
I (..\..\Middlewares\FATFS\source\ff.h)(0x684ED0B5)
I (..\..\Middlewares\FATFS\source\ffconf.h)(0x684ED0B5)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Middlewares\FATFS\source\diskio.h)(0x684ED0B5)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdarg.h)(0x5E8E3CC2)
F (..\..\Middlewares\FATFS\source\ffsystem.c)(0x684ED0B5)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\ffsystem.o --omf_browse ..\..\output\ffsystem.crf --depend ..\..\output\ffsystem.d)
I (..\..\Middlewares\./MALLOC/malloc.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Middlewares\./FATFS/source/ff.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ffconf.h)(0x684ED0B5)
F (..\..\Middlewares\FATFS\source\ffunicode.c)(0x684ED0B6)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\ffunicode.o --omf_browse ..\..\output\ffunicode.crf --depend ..\..\output\ffunicode.d)
I (..\..\Middlewares\FATFS\source\ff.h)(0x684ED0B5)
I (..\..\Middlewares\FATFS\source\ffconf.h)(0x684ED0B5)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
F (..\..\Middlewares\FATFS\exfuns\exfuns.c)(0x684ED0B5)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\exfuns.o --omf_browse ..\..\output\exfuns.crf --depend ..\..\output\exfuns.d)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\string.h)(0x5E8E3CC2)
I (..\..\Middlewares\./MALLOC/malloc.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
I (..\..\Middlewares\./FATFS/exfuns/exfuns.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ff.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ffconf.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/exfuns/fattester.h)(0x684ED0B5)
F (..\..\Middlewares\FATFS\exfuns\fattester.c)(0x684ED0B5)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\fattester.o --omf_browse ..\..\output\fattester.crf --depend ..\..\output\fattester.d)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\string.h)(0x5E8E3CC2)
I (..\..\Middlewares\./MALLOC/malloc.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
I (..\..\Drivers\./SYSTEM/usart/usart.h)(0x684ED0B4)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdio.h)(0x5E8E3CC2)
I (..\..\Middlewares\./FATFS/exfuns/exfuns.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ff.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/source/ffconf.h)(0x684ED0B5)
I (..\..\Middlewares\./FATFS/exfuns/fattester.h)(0x684ED0B5)
I (..\..\Drivers\./BSP/SDIO/sdio_sdcard.h)(0x684ED0B2)
F (..\..\Middlewares\MALLOC\malloc.c)(0x684ED0B3)(--c99 -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\..\Drivers\CMSIS -I ..\..\Drivers\CMSIS\GD\GD32F4xx\Include -I ..\..\Drivers\GD32F4xx_standard_peripheral\Include -I ..\..\Drivers -I ..\..\Middlewares -I ..\..\User

-I"C:\keil arm\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include"

-D__UVISION_VERSION="542" -DGD32F470 -DGD32F470

-o ..\..\output\malloc.o --omf_browse ..\..\output\malloc.crf --depend ..\..\output\malloc.d)
I (..\..\Middlewares\./MALLOC/malloc.h)(0x684ED0B2)
I (..\..\Drivers\./SYSTEM/sys/sys.h)(0x684ED0B5)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684ED0B6)
I (..\..\Drivers\CMSIS\core_cm4.h)(0x684ED0B1)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (..\..\Drivers\CMSIS\cmsis_version.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_compiler.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\cmsis_armcc.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\mpu_armv7.h)(0x684ED0B1)
I (..\..\Drivers\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684ED0B6)
I (..\..\User\gd32f4xx_libopt.h)(0x684ED0B1)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x684ED0B3)
I (C:\keil arm\ARM\ARM_Compiler_5.06u7\include\stdlib.h)(0x5E8E3CC2)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x684ED0B3)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x684ED0B4)
I (..\..\Drivers\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x684ED0B3)
F (..\..\readme.txt)(0x684ED0AB)()
