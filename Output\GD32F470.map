Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_gd32f450_470.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to rtc.o(i.RTC_WKUP_IRQHandler) for RTC_WKUP_IRQHandler
    startup_gd32f450_470.o(RESET) refers to usart.o(i.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to rtc.o(i.RTC_Alarm_IRQHandler) for RTC_Alarm_IRQHandler
    startup_gd32f450_470.o(RESET) refers to sd_conf.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    startup_gd32f450_470.o(RESET) refers to main.o(i.TIMER6_IRQHandler) for TIMER6_IRQHandler
    startup_gd32f450_470.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to __main.o(!!!main) for __main
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(HEAP) for Heap_Mem
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(STACK) for Stack_Mem
    main.o(i.TIMER6_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    main.o(i.TIMER6_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    main.o(i.TIMER6_IRQHandler) refers to main.o(.data) for timer6_counter
    main.o(i.adjust_sample_cycle) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.adjust_sample_cycle) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.adjust_sample_cycle) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.adjust_sample_cycle) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    main.o(i.adjust_sample_cycle) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    main.o(i.adjust_sample_cycle) refers to timer.o(i.timerx_int_init) for timerx_int_init
    main.o(i.adjust_sample_cycle) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    main.o(i.adjust_sample_cycle) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.adjust_sample_cycle) refers to main.o(i.store_log_entry) for store_log_entry
    main.o(i.adjust_sample_cycle) refers to main.o(i.save_config_to_flash) for save_config_to_flash
    main.o(i.adjust_sample_cycle) refers to main.o(.data) for sys_config
    main.o(i.datetime_to_unix) refers to mktime.o(.text) for mktime
    main.o(i.get_current_time) refers to rtc.o(i.rtc_get_date) for rtc_get_date
    main.o(i.get_current_time) refers to rtc.o(i.rtc_get_time) for rtc_get_time
    main.o(i.get_system_time) refers to sys.o(i.sys_tick_get) for sys_tick_get
    main.o(i.get_system_time) refers to main.o(.data) for last_tick
    main.o(i.main) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.main) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.main) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.main) refers to _printf_str.o(.text) for _printf_str
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to usart.o(i.usart_init) for usart_init
    main.o(i.main) refers to led.o(i.led_init) for led_init
    main.o(i.main) refers to key.o(i.key_init) for key_init
    main.o(i.main) refers to oled.o(i.oled_init) for oled_init
    main.o(i.main) refers to adc.o(i.adc_init) for adc_init
    main.o(i.main) refers to norflash.o(i.norflash_init) for norflash_init
    main.o(i.main) refers to rtc.o(i.rtc_config) for rtc_config
    main.o(i.main) refers to timer.o(i.timerx_int_init) for timerx_int_init
    main.o(i.main) refers to ff.o(i.f_mount) for f_mount
    main.o(i.main) refers to main.o(i.read_config_from_flash) for read_config_from_flash
    main.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.main) refers to main.o(i.save_config_to_flash) for save_config_to_flash
    main.o(i.main) refers to main.o(i.store_log_entry) for store_log_entry
    main.o(i.main) refers to main.o(i.oled_display_status) for oled_display_status
    main.o(i.main) refers to key.o(i.key_scan) for key_scan
    main.o(i.main) refers to main.o(i.stop_sampling) for stop_sampling
    main.o(i.main) refers to main.o(i.start_sampling) for start_sampling
    main.o(i.main) refers to main.o(i.adjust_sample_cycle) for adjust_sample_cycle
    main.o(i.main) refers to strcmpv7m.o(.text) for strcmp
    main.o(i.main) refers to main.o(i.system_self_test) for system_self_test
    main.o(i.main) refers to strncmp.o(.text) for strncmp
    main.o(i.main) refers to main.o(i.set_rtc_time) for set_rtc_time
    main.o(i.main) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.main) refers to main.o(i.read_config_file) for read_config_file
    main.o(i.main) refers to main.o(i.set_ratio) for set_ratio
    main.o(i.main) refers to main.o(i.set_limit) for set_limit
    main.o(i.main) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    main.o(i.main) refers to main.o(i.update_oled_time) for update_oled_time
    main.o(i.main) refers to main.o(i.process_adc_sample) for process_adc_sample
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to main.o(.data) for sys_config
    main.o(i.main) refers to usart.o(.data) for g_usart_rx_sta
    main.o(i.main) refers to usart.o(.bss) for g_usart_rx_buf
    main.o(i.oled_display_status) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.oled_display_status) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.oled_display_status) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.oled_display_status) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.oled_display_status) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.oled_display_status) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.oled_display_status) refers to oled.o(i.oled_clear) for oled_clear
    main.o(i.oled_display_status) refers to oled.o(i.oled_show_string) for oled_show_string
    main.o(i.oled_display_status) refers to oled.o(i.oled_refresh_gram) for oled_refresh_gram
    main.o(i.oled_display_status) refers to main.o(.data) for sampling_enabled
    main.o(i.process_adc_sample) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.process_adc_sample) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.process_adc_sample) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.process_adc_sample) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.process_adc_sample) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.process_adc_sample) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.process_adc_sample) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    main.o(i.process_adc_sample) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    main.o(i.process_adc_sample) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.process_adc_sample) refers to _printf_str.o(.text) for _printf_str
    main.o(i.process_adc_sample) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.process_adc_sample) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.process_adc_sample) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.process_adc_sample) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    main.o(i.process_adc_sample) refers to main.o(i.store_overlimit_data) for store_overlimit_data
    main.o(i.process_adc_sample) refers to main.o(i.store_sample_data) for store_sample_data
    main.o(i.process_adc_sample) refers to main.o(i.datetime_to_unix) for datetime_to_unix
    main.o(i.process_adc_sample) refers to main.o(i.store_hide_data) for store_hide_data
    main.o(i.process_adc_sample) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.process_adc_sample) refers to main.o(.data) for oled_voltage
    main.o(i.read_config_file) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    main.o(i.read_config_file) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.read_config_file) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.read_config_file) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.read_config_file) refers to ff.o(i.f_open) for f_open
    main.o(i.read_config_file) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.read_config_file) refers to strstr.o(.text) for strstr
    main.o(i.read_config_file) refers to __0sscanf.o(.text) for __0sscanf
    main.o(i.read_config_file) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.read_config_file) refers to ff.o(i.f_gets) for f_gets
    main.o(i.read_config_file) refers to ff.o(i.f_close) for f_close
    main.o(i.read_config_file) refers to main.o(.data) for sys_config
    main.o(i.read_config_from_flash) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.read_config_from_flash) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.read_config_from_flash) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.read_config_from_flash) refers to norflash.o(i.norflash_read) for norflash_read
    main.o(i.read_config_from_flash) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.read_config_from_flash) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.read_config_from_flash) refers to main.o(.data) for sys_config
    main.o(i.save_config_to_flash) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.save_config_to_flash) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.save_config_to_flash) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.save_config_to_flash) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.save_config_to_flash) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.save_config_to_flash) refers to norflash.o(i.norflash_erase_sector) for norflash_erase_sector
    main.o(i.save_config_to_flash) refers to norflash.o(i.norflash_write) for norflash_write
    main.o(i.save_config_to_flash) refers to main.o(i.store_log_entry) for store_log_entry
    main.o(i.save_config_to_flash) refers to main.o(.data) for sys_config
    main.o(i.set_limit) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.set_limit) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.set_limit) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.set_limit) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.set_limit) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.set_limit) refers to key.o(i.key_scan) for key_scan
    main.o(i.set_limit) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    main.o(i.set_limit) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(i.set_limit) refers to main.o(i.store_log_entry) for store_log_entry
    main.o(i.set_limit) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.set_limit) refers to main.o(.data) for sys_config
    main.o(i.set_ratio) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.set_ratio) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.set_ratio) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.set_ratio) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.set_ratio) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.set_ratio) refers to key.o(i.key_scan) for key_scan
    main.o(i.set_ratio) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    main.o(i.set_ratio) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(i.set_ratio) refers to main.o(i.store_log_entry) for store_log_entry
    main.o(i.set_ratio) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.set_ratio) refers to main.o(.data) for sys_config
    main.o(i.set_rtc_time) refers to _scanf_int.o(.text) for _scanf_int
    main.o(i.set_rtc_time) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.set_rtc_time) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.set_rtc_time) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.set_rtc_time) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.set_rtc_time) refers to __0sscanf.o(.text) for __0sscanf
    main.o(i.set_rtc_time) refers to rtc.o(i.rtc_set_date) for rtc_set_date
    main.o(i.set_rtc_time) refers to rtc.o(i.rtc_set_time) for rtc_set_time
    main.o(i.set_rtc_time) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.start_sampling) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.start_sampling) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.start_sampling) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.start_sampling) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.start_sampling) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    main.o(i.start_sampling) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.start_sampling) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.start_sampling) refers to oled.o(i.oled_clear) for oled_clear
    main.o(i.start_sampling) refers to oled.o(i.oled_show_string) for oled_show_string
    main.o(i.start_sampling) refers to oled.o(i.oled_refresh_gram) for oled_refresh_gram
    main.o(i.start_sampling) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    main.o(i.start_sampling) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.start_sampling) refers to main.o(i.store_log_entry) for store_log_entry
    main.o(i.start_sampling) refers to main.o(.data) for sampling_enabled
    main.o(i.stop_sampling) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    main.o(i.stop_sampling) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    main.o(i.stop_sampling) refers to oled.o(i.oled_clear) for oled_clear
    main.o(i.stop_sampling) refers to oled.o(i.oled_show_string) for oled_show_string
    main.o(i.stop_sampling) refers to oled.o(i.oled_refresh_gram) for oled_refresh_gram
    main.o(i.stop_sampling) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.stop_sampling) refers to ff.o(i.f_close) for f_close
    main.o(i.stop_sampling) refers to main.o(i.store_log_entry) for store_log_entry
    main.o(i.stop_sampling) refers to main.o(.data) for sampling_enabled
    main.o(i.stop_sampling) refers to main.o(.bss) for sample_file
    main.o(i.store_hide_data) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.store_hide_data) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.store_hide_data) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.store_hide_data) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.store_hide_data) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    main.o(i.store_hide_data) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    main.o(i.store_hide_data) refers to ff.o(i.f_close) for f_close
    main.o(i.store_hide_data) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.store_hide_data) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.store_hide_data) refers to ff.o(i.f_mkdir) for f_mkdir
    main.o(i.store_hide_data) refers to ff.o(i.f_open) for f_open
    main.o(i.store_hide_data) refers to main.o(i.encode_voltage) for encode_voltage
    main.o(i.store_hide_data) refers to strcat.o(.text) for strcat
    main.o(i.store_hide_data) refers to strlen.o(.text) for strlen
    main.o(i.store_hide_data) refers to ff.o(i.f_write) for f_write
    main.o(i.store_hide_data) refers to ff.o(i.f_sync) for f_sync
    main.o(i.store_hide_data) refers to main.o(.data) for hide_count
    main.o(i.store_hide_data) refers to main.o(.bss) for hide_file
    main.o(i.store_log_entry) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.store_log_entry) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(i.store_log_entry) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.store_log_entry) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.store_log_entry) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.store_log_entry) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.store_log_entry) refers to _printf_str.o(.text) for _printf_str
    main.o(i.store_log_entry) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.store_log_entry) refers to ff.o(i.f_mkdir) for f_mkdir
    main.o(i.store_log_entry) refers to ff.o(i.f_open) for f_open
    main.o(i.store_log_entry) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.store_log_entry) refers to strlen.o(.text) for strlen
    main.o(i.store_log_entry) refers to ff.o(i.f_write) for f_write
    main.o(i.store_log_entry) refers to ff.o(i.f_sync) for f_sync
    main.o(i.store_log_entry) refers to main.o(.data) for log_initialized
    main.o(i.store_log_entry) refers to main.o(.bss) for current_filename
    main.o(i.store_overlimit_data) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.store_overlimit_data) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.store_overlimit_data) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.store_overlimit_data) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.store_overlimit_data) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.store_overlimit_data) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.store_overlimit_data) refers to ff.o(i.f_close) for f_close
    main.o(i.store_overlimit_data) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.store_overlimit_data) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.store_overlimit_data) refers to ff.o(i.f_mkdir) for f_mkdir
    main.o(i.store_overlimit_data) refers to ff.o(i.f_open) for f_open
    main.o(i.store_overlimit_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.store_overlimit_data) refers to strlen.o(.text) for strlen
    main.o(i.store_overlimit_data) refers to ff.o(i.f_write) for f_write
    main.o(i.store_overlimit_data) refers to ff.o(i.f_sync) for f_sync
    main.o(i.store_overlimit_data) refers to main.o(.data) for overlimit_count
    main.o(i.store_overlimit_data) refers to main.o(.bss) for overlimit_file
    main.o(i.store_sample_data) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.store_sample_data) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.store_sample_data) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.store_sample_data) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.store_sample_data) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.store_sample_data) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.store_sample_data) refers to ff.o(i.f_close) for f_close
    main.o(i.store_sample_data) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.store_sample_data) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.store_sample_data) refers to ff.o(i.f_mkdir) for f_mkdir
    main.o(i.store_sample_data) refers to ff.o(i.f_open) for f_open
    main.o(i.store_sample_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.store_sample_data) refers to strlen.o(.text) for strlen
    main.o(i.store_sample_data) refers to ff.o(i.f_write) for f_write
    main.o(i.store_sample_data) refers to ff.o(i.f_sync) for f_sync
    main.o(i.store_sample_data) refers to main.o(.data) for sample_count
    main.o(i.store_sample_data) refers to main.o(.bss) for sample_file
    main.o(i.system_self_test) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.system_self_test) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.system_self_test) refers to _printf_str.o(.text) for _printf_str
    main.o(i.system_self_test) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.system_self_test) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    main.o(i.system_self_test) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    main.o(i.system_self_test) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(i.system_self_test) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.system_self_test) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.system_self_test) refers to norflash.o(i.norflash_read_id) for norflash_read_id
    main.o(i.system_self_test) refers to sd_conf.o(i.sdio_sd_init) for sdio_sd_init
    main.o(i.system_self_test) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.system_self_test) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.system_self_test) refers to sdio_sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    main.o(i.update_oled_time) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.update_oled_time) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.update_oled_time) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.update_oled_time) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.update_oled_time) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.update_oled_time) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.update_oled_time) refers to main.o(i.get_current_time) for get_current_time
    main.o(i.update_oled_time) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.update_oled_time) refers to oled.o(i.oled_show_string) for oled_show_string
    main.o(i.update_oled_time) refers to adc.o(i.adc_get_result_average) for adc_get_result_average
    main.o(i.update_oled_time) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.update_oled_time) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.update_oled_time) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(i.update_oled_time) refers to oled.o(i.oled_refresh_gram) for oled_refresh_gram
    main.o(i.update_oled_time) refers to main.o(.data) for oled_voltage
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    delay.o(i.delay_init) refers to delay.o(.data) for g_fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for g_fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.rrx_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART0_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    usart.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.USART0_IRQHandler) refers to usart.o(.data) for g_usart_rx_sta
    usart.o(i.USART0_IRQHandler) refers to usart.o(.bss) for g_usart_rx_buf
    usart.o(i._sys_command_string) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._ttywrch) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.usart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.usart_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.usart_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.usart_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.usart_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.usart_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    led.o(i.led_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    led.o(i.led_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    led.o(i.led_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    led.o(i.led_init) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.iic_ack) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.iic_ack) refers to myiic.o(i.iic_delay) for iic_delay
    myiic.o(i.iic_delay) refers to delay.o(i.delay_us) for delay_us
    myiic.o(i.iic_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    myiic.o(i.iic_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    myiic.o(i.iic_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    myiic.o(i.iic_init) refers to myiic.o(i.iic_stop) for iic_stop
    myiic.o(i.iic_nack) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.iic_nack) refers to myiic.o(i.iic_delay) for iic_delay
    myiic.o(i.iic_read_byte) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.iic_read_byte) refers to myiic.o(i.iic_delay) for iic_delay
    myiic.o(i.iic_read_byte) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    myiic.o(i.iic_read_byte) refers to myiic.o(i.iic_nack) for iic_nack
    myiic.o(i.iic_read_byte) refers to myiic.o(i.iic_ack) for iic_ack
    myiic.o(i.iic_send_byte) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.iic_send_byte) refers to myiic.o(i.iic_delay) for iic_delay
    myiic.o(i.iic_start) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.iic_start) refers to myiic.o(i.iic_delay) for iic_delay
    myiic.o(i.iic_stop) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.iic_stop) refers to myiic.o(i.iic_delay) for iic_delay
    myiic.o(i.iic_wait_ack) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.iic_wait_ack) refers to myiic.o(i.iic_delay) for iic_delay
    oled.o(i.OLED_Show_Font) refers to oled.o(i.oled_draw_point) for oled_draw_point
    oled.o(i.OLED_Show_Font) refers to oled.o(.constdata) for OLED_HZK_XMZB
    oled.o(i.oled_clear) refers to oled.o(i.oled_refresh_gram) for oled_refresh_gram
    oled.o(i.oled_clear) refers to oled.o(.bss) for g_oled_gram
    oled.o(i.oled_display_off) refers to oled.o(i.oled_wr_byte) for oled_wr_byte
    oled.o(i.oled_display_on) refers to oled.o(i.oled_wr_byte) for oled_wr_byte
    oled.o(i.oled_draw_point) refers to oled.o(.bss) for g_oled_gram
    oled.o(i.oled_fill) refers to oled.o(i.oled_draw_point) for oled_draw_point
    oled.o(i.oled_fill) refers to oled.o(i.oled_refresh_gram) for oled_refresh_gram
    oled.o(i.oled_init) refers to myiic.o(i.iic_init) for iic_init
    oled.o(i.oled_init) refers to oled.o(i.oled_wr_byte) for oled_wr_byte
    oled.o(i.oled_init) refers to oled.o(i.oled_clear) for oled_clear
    oled.o(i.oled_refresh_gram) refers to oled.o(i.oled_wr_byte) for oled_wr_byte
    oled.o(i.oled_refresh_gram) refers to myiic.o(i.iic_start) for iic_start
    oled.o(i.oled_refresh_gram) refers to myiic.o(i.iic_send_byte) for iic_send_byte
    oled.o(i.oled_refresh_gram) refers to myiic.o(i.iic_wait_ack) for iic_wait_ack
    oled.o(i.oled_refresh_gram) refers to myiic.o(i.iic_stop) for iic_stop
    oled.o(i.oled_refresh_gram) refers to oled.o(.bss) for g_oled_gram
    oled.o(i.oled_show_char) refers to oled.o(i.oled_draw_point) for oled_draw_point
    oled.o(i.oled_show_char) refers to oled.o(.constdata) for oled_asc2_1206
    oled.o(i.oled_show_num) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.oled_show_num) refers to oled.o(i.oled_show_char) for oled_show_char
    oled.o(i.oled_show_string) refers to oled.o(i.oled_clear) for oled_clear
    oled.o(i.oled_show_string) refers to oled.o(i.oled_show_char) for oled_show_char
    oled.o(i.oled_wr_byte) refers to myiic.o(i.iic_start) for iic_start
    oled.o(i.oled_wr_byte) refers to myiic.o(i.iic_send_byte) for iic_send_byte
    oled.o(i.oled_wr_byte) refers to myiic.o(i.iic_wait_ack) for iic_wait_ack
    oled.o(i.oled_wr_byte) refers to myiic.o(i.iic_stop) for iic_stop
    key.o(i.key_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    key.o(i.key_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    key.o(i.key_scan) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    key.o(i.key_scan) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.key_scan) refers to key.o(.data) for key_up
    adc.o(i.adc_get_result) refers to gd32f4xx_adc.o(i.adc_routine_channel_config) for adc_routine_channel_config
    adc.o(i.adc_get_result) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    adc.o(i.adc_get_result) refers to gd32f4xx_adc.o(i.adc_flag_get) for adc_flag_get
    adc.o(i.adc_get_result) refers to gd32f4xx_adc.o(i.adc_flag_clear) for adc_flag_clear
    adc.o(i.adc_get_result) refers to gd32f4xx_adc.o(i.adc_routine_data_read) for adc_routine_data_read
    adc.o(i.adc_get_result_average) refers to adc.o(i.adc_get_result) for adc_get_result
    adc.o(i.adc_get_result_average) refers to delay.o(i.delay_ms) for delay_ms
    adc.o(i.adc_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.adc_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_deinit) for adc_deinit
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.adc_init) refers to delay.o(i.delay_ms) for delay_ms
    adc.o(i.adc_init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    fmc.o(i.fmc_erase_sector) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    fmc.o(i.fmc_erase_sector) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    fmc.o(i.fmc_erase_sector) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    fmc.o(i.fmc_erase_sector) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    fmc.o(i.fmc_write_32bit_data) refers to fmc.o(i.fmc_sector_get) for fmc_sector_get
    fmc.o(i.fmc_write_32bit_data) refers to fmc.o(i.sector_name_to_number) for sector_name_to_number
    fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    fmc.o(i.test_write) refers to fmc.o(i.fmc_write_32bit_data) for fmc_write_32bit_data
    norflash.o(i.norflash_erase_chip) refers to norflash.o(i.norflash_write_enable) for norflash_write_enable
    norflash.o(i.norflash_erase_chip) refers to norflash.o(i.norflash_wait_busy) for norflash_wait_busy
    norflash.o(i.norflash_erase_chip) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_erase_chip) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_erase_sector) refers to norflash.o(i.norflash_write_enable) for norflash_write_enable
    norflash.o(i.norflash_erase_sector) refers to norflash.o(i.norflash_wait_busy) for norflash_wait_busy
    norflash.o(i.norflash_erase_sector) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_erase_sector) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_erase_sector) refers to norflash.o(i.norflash_send_address) for norflash_send_address
    norflash.o(i.norflash_init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    norflash.o(i.norflash_init) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    norflash.o(i.norflash_init) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    norflash.o(i.norflash_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    norflash.o(i.norflash_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    norflash.o(i.norflash_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    norflash.o(i.norflash_init) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_init) refers to spi.o(i.spi1_init) for spi1_init
    norflash.o(i.norflash_init) refers to spi.o(i.spi1_set_speed) for spi1_set_speed
    norflash.o(i.norflash_init) refers to norflash.o(i.norflash_read_id) for norflash_read_id
    norflash.o(i.norflash_init) refers to norflash.o(i.norflash_read_sr) for norflash_read_sr
    norflash.o(i.norflash_init) refers to norflash.o(i.norflash_write_enable) for norflash_write_enable
    norflash.o(i.norflash_init) refers to norflash.o(i.norflash_write_sr) for norflash_write_sr
    norflash.o(i.norflash_init) refers to delay.o(i.delay_ms) for delay_ms
    norflash.o(i.norflash_init) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_init) refers to noretval__2printf.o(.text) for __2printf
    norflash.o(i.norflash_init) refers to norflash.o(.data) for g_norflash_type
    norflash.o(i.norflash_read) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_read) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_read) refers to norflash.o(i.norflash_send_address) for norflash_send_address
    norflash.o(i.norflash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_read_id) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_read_sr) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_read_sr) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_send_address) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_send_address) refers to norflash.o(.data) for g_norflash_type
    norflash.o(i.norflash_wait_busy) refers to norflash.o(i.norflash_read_sr) for norflash_read_sr
    norflash.o(i.norflash_write) refers to norflash.o(i.norflash_read) for norflash_read
    norflash.o(i.norflash_write) refers to norflash.o(i.norflash_erase_sector) for norflash_erase_sector
    norflash.o(i.norflash_write) refers to norflash.o(i.norflash_write_nocheck) for norflash_write_nocheck
    norflash.o(i.norflash_write) refers to norflash.o(.bss) for g_norflash_buf
    norflash.o(i.norflash_write_disable) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_write_disable) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_write_enable) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_write_nocheck) refers to norflash.o(i.norflash_write_page) for norflash_write_page
    norflash.o(i.norflash_write_page) refers to norflash.o(i.norflash_write_enable) for norflash_write_enable
    norflash.o(i.norflash_write_page) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_write_page) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    norflash.o(i.norflash_write_page) refers to norflash.o(i.norflash_send_address) for norflash_send_address
    norflash.o(i.norflash_write_page) refers to norflash.o(i.norflash_wait_busy) for norflash_wait_busy
    norflash.o(i.norflash_write_sr) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    norflash.o(i.norflash_write_sr) refers to spi.o(i.spi1_read_write_byte) for spi1_read_write_byte
    rtc.o(i.RTC_Alarm_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_get) for rtc_flag_get
    rtc.o(i.RTC_Alarm_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    rtc.o(i.RTC_Alarm_IRQHandler) refers to noretval__2printf.o(.text) for __2printf
    rtc.o(i.RTC_Alarm_IRQHandler) refers to gd32f4xx_exti.o(i.exti_flag_clear) for exti_flag_clear
    rtc.o(i.RTC_WKUP_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_get) for rtc_flag_get
    rtc.o(i.RTC_WKUP_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    rtc.o(i.RTC_WKUP_IRQHandler) refers to gd32f4xx_gpio.o(i.gpio_bit_toggle) for gpio_bit_toggle
    rtc.o(i.RTC_WKUP_IRQHandler) refers to gd32f4xx_exti.o(i.exti_flag_clear) for exti_flag_clear
    rtc.o(i.rtc_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.rtc_config) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.rtc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    rtc.o(i.rtc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    rtc.o(i.rtc_config) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    rtc.o(i.rtc_config) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    rtc.o(i.rtc_config) refers to rtc.o(i.rtc_set_time) for rtc_set_time
    rtc.o(i.rtc_config) refers to rtc.o(i.rtc_set_date) for rtc_set_date
    rtc.o(i.rtc_config) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_get_date) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    rtc.o(i.rtc_get_date) refers to rtc.o(i.rtc_bcd2dec) for rtc_bcd2dec
    rtc.o(i.rtc_get_date) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_get_time) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    rtc.o(i.rtc_get_time) refers to rtc.o(i.rtc_bcd2dec) for rtc_bcd2dec
    rtc.o(i.rtc_get_time) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_get_week) refers to rtc.o(.constdata) for table_week
    rtc.o(i.rtc_set_alarma) refers to gd32f4xx_rtc.o(i.rtc_alarm_disable) for rtc_alarm_disable
    rtc.o(i.rtc_set_alarma) refers to rtc.o(i.rtc_dec2bcd) for rtc_dec2bcd
    rtc.o(i.rtc_set_alarma) refers to gd32f4xx_rtc.o(i.rtc_alarm_config) for rtc_alarm_config
    rtc.o(i.rtc_set_alarma) refers to gd32f4xx_rtc.o(i.rtc_interrupt_enable) for rtc_interrupt_enable
    rtc.o(i.rtc_set_alarma) refers to gd32f4xx_rtc.o(i.rtc_alarm_enable) for rtc_alarm_enable
    rtc.o(i.rtc_set_alarma) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    rtc.o(i.rtc_set_alarma) refers to gd32f4xx_exti.o(i.exti_flag_clear) for exti_flag_clear
    rtc.o(i.rtc_set_alarma) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    rtc.o(i.rtc_set_alarma) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    rtc.o(i.rtc_set_date) refers to rtc.o(i.rtc_dec2bcd) for rtc_dec2bcd
    rtc.o(i.rtc_set_date) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_set_date) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_set_time) refers to rtc.o(i.rtc_dec2bcd) for rtc_dec2bcd
    rtc.o(i.rtc_set_time) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_set_time) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_rtc.o(i.rtc_wakeup_disable) for rtc_wakeup_disable
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_rtc.o(i.rtc_wakeup_clock_set) for rtc_wakeup_clock_set
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_rtc.o(i.rtc_wakeup_timer_set) for rtc_wakeup_timer_set
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_rtc.o(i.rtc_interrupt_enable) for rtc_interrupt_enable
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_rtc.o(i.rtc_wakeup_enable) for rtc_wakeup_enable
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_exti.o(i.exti_flag_clear) for exti_flag_clear
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    rtc.o(i.rtc_set_wakeup) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    sd_conf.o(i.SDIO_IRQHandler) refers to sdio_sdcard.o(i.sd_interrupts_process) for sd_interrupts_process
    sd_conf.o(i.card_info_get) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    sd_conf.o(i.card_info_get) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    sd_conf.o(i.card_info_get) refers to _printf_dec.o(.text) for _printf_int_dec
    sd_conf.o(i.card_info_get) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    sd_conf.o(i.card_info_get) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    sd_conf.o(i.card_info_get) refers to noretval__2printf.o(.text) for __2printf
    sd_conf.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    sd_conf.o(i.card_info_get) refers to sdio_sdcard.o(.data) for sd_scr
    sd_conf.o(i.card_info_get) refers to sd_conf.o(.bss) for sd_cardinfo
    sd_conf.o(i.sd_read_disk) refers to sdio_sdcard.o(i.sd_block_read) for sd_block_read
    sd_conf.o(i.sd_read_disk) refers to sdio_sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    sd_conf.o(i.sd_read_udisk) refers to sdio_sdcard.o(i.sd_block_read) for sd_block_read
    sd_conf.o(i.sd_read_udisk) refers to sdio_sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    sd_conf.o(i.sd_write_disk) refers to sdio_sdcard.o(i.sd_block_write) for sd_block_write
    sd_conf.o(i.sd_write_disk) refers to sdio_sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    sd_conf.o(i.sd_write_udisk) refers to sdio_sdcard.o(i.sd_block_write) for sd_block_write
    sd_conf.o(i.sd_write_udisk) refers to sdio_sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    sd_conf.o(i.sdio_sd_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    sd_conf.o(i.sdio_sd_init) refers to sdio_sdcard.o(i.sd_init) for sd_init
    sd_conf.o(i.sdio_sd_init) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    sd_conf.o(i.sdio_sd_init) refers to sdio_sdcard.o(i.sd_card_select_deselect) for sd_card_select_deselect
    sd_conf.o(i.sdio_sd_init) refers to sdio_sdcard.o(i.sd_cardstatus_get) for sd_cardstatus_get
    sd_conf.o(i.sdio_sd_init) refers to noretval__2printf.o(.text) for __2printf
    sd_conf.o(i.sdio_sd_init) refers to sdio_sdcard.o(i.sd_lock_unlock) for sd_lock_unlock
    sd_conf.o(i.sdio_sd_init) refers to sdio_sdcard.o(i.sd_bus_mode_config) for sd_bus_mode_config
    sd_conf.o(i.sdio_sd_init) refers to sdio_sdcard.o(i.sd_transfer_mode_config) for sd_transfer_mode_config
    sd_conf.o(i.sdio_sd_init) refers to sd_conf.o(.bss) for sd_cardinfo
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r1_error_check) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.r2_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r3_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r7_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(i.sd_bus_width_config) for sd_bus_width_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.sd_scr_get) for sd_scr_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(.data) for sd_scr
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_power_state_get) for sdio_power_state_get
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r2_error_check) for r2_error_check
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r6_error_check) for r6_error_check
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_select_deselect) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.rcu_config) for rcu_config
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.gpio_config) for gpio_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_deinit) for sdio_deinit
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_power_on) for sd_power_on
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_card_init) for sd_card_init
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_get) for sdio_interrupt_flag_get
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(i.sd_transfer_stop) for sd_transfer_stop
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear) for sdio_interrupt_flag_clear
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_disable) for sdio_interrupt_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_power_off) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_enable) for sdio_clock_enable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.cmdsent_error_check) for cmdsent_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r7_error_check) for r7_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r3_error_check) for r3_error_check
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_transfer_mode_config) refers to sdio_sdcard.o(.data) for transmode
    sdio_sdcard.o(i.sd_transfer_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_transfer_stop) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    spi.o(i.spi1_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    spi.o(i.spi1_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    spi.o(i.spi1_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    spi.o(i.spi1_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    spi.o(i.spi1_init) refers to gd32f4xx_spi.o(i.spi_i2s_deinit) for spi_i2s_deinit
    spi.o(i.spi1_init) refers to gd32f4xx_spi.o(i.spi_struct_para_init) for spi_struct_para_init
    spi.o(i.spi1_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    spi.o(i.spi1_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    spi.o(i.spi1_read_write_byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi.o(i.spi1_read_write_byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi.o(i.spi1_read_write_byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi.o(i.spi1_set_speed) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    spi.o(i.spi1_set_speed) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    timer.o(i.timerx_disable) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    timer.o(i.timerx_enable) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    timer.o(i.timerx_int_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    timer.o(i.timerx_int_init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    timer.o(i.timerx_int_init) refers to gd32f4xx_timer.o(i.timer_struct_para_init) for timer_struct_para_init
    timer.o(i.timerx_int_init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    timer.o(i.timerx_int_init) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    timer.o(i.timerx_int_init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    timer.o(i.timerx_int_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    timer.o(i.timerx_set_period) refers to gd32f4xx_timer.o(i.timer_autoreload_value_config) for timer_autoreload_value_config
    diskio.o(i.disk_initialize) refers to sd_conf.o(i.sdio_sd_init) for sdio_sd_init
    diskio.o(i.disk_initialize) refers to norflash.o(i.norflash_init) for norflash_init
    diskio.o(i.disk_ioctl) refers to sdio_sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    diskio.o(i.disk_ioctl) refers to sd_conf.o(.bss) for sd_cardinfo
    diskio.o(i.disk_read) refers to sd_conf.o(i.sd_read_disk) for sd_read_disk
    diskio.o(i.disk_read) refers to sd_conf.o(i.sdio_sd_init) for sdio_sd_init
    diskio.o(i.disk_read) refers to norflash.o(i.norflash_read) for norflash_read
    diskio.o(i.disk_write) refers to sd_conf.o(i.sd_write_disk) for sd_write_disk
    diskio.o(i.disk_write) refers to sd_conf.o(i.sdio_sd_init) for sdio_sd_init
    diskio.o(i.disk_write) refers to norflash.o(i.norflash_write) for norflash_write
    ff.o(i.change_bitmap) refers to ff.o(i.move_window) for move_window
    ff.o(i.check_fs) refers to ff.o(i.move_window) for move_window
    ff.o(i.check_fs) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.check_fs) refers to memcmp.o(.text) for memcmp
    ff.o(i.check_fs) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.clmt_clust) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ff.o(i.cmp_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.cmp_lfn) refers to ffunicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.find_bitmap) for find_bitmap
    ff.o(i.create_chain) refers to ff.o(i.change_bitmap) for change_bitmap
    ff.o(i.create_chain) refers to ff.o(i.fill_last_frag) for fill_last_frag
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to ff.o(i.tchar2uni) for tchar2uni
    ff.o(i.create_name) refers to strchr.o(.text) for strchr
    ff.o(i.create_name) refers to aeabi_memset.o(.text) for __aeabi_memset
    ff.o(i.create_name) refers to ffunicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.create_name) refers to ffunicode.o(i.ff_uni2oem) for ff_uni2oem
    ff.o(i.create_partition) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.create_partition) refers to rt_memclr.o(.text) for __aeabi_memclr
    ff.o(i.create_partition) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.create_partition) refers to ff.o(i.st_word) for st_word
    ff.o(i.create_partition) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.create_xdir) refers to rt_memclr.o(.text) for __aeabi_memclr
    ff.o(i.create_xdir) refers to ff.o(i.st_word) for st_word
    ff.o(i.create_xdir) refers to ff.o(i.xname_sum) for xname_sum
    ff.o(i.dbc_1st) refers to ff.o(.constdata) for DbcTbl
    ff.o(i.dbc_2nd) refers to ff.o(.constdata) for DbcTbl
    ff.o(i.dir_alloc) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_alloc) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_alloc) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_clear) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.dir_clear) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.dir_clear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ff.o(i.dir_clear) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.dir_clear) refers to rt_memclr.o(.text) for __aeabi_memclr
    ff.o(i.dir_clear) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.dir_clear) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.xname_sum) for xname_sum
    ff.o(i.dir_find) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.dir_find) refers to ffunicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dir_find) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to memcmp.o(.text) for memcmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.dir_clear) for dir_clear
    ff.o(i.dir_next) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.load_xdir) for load_xdir
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.dir_alloc) for dir_alloc
    ff.o(i.dir_register) refers to ff.o(i.fill_first_frag) for fill_first_frag
    ff.o(i.dir_register) refers to ff.o(i.fill_last_frag) for fill_last_frag
    ff.o(i.dir_register) refers to ff.o(i.load_obj_xdir) for load_obj_xdir
    ff.o(i.dir_register) refers to ff.o(i.st_qword) for st_qword
    ff.o(i.dir_register) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.dir_register) refers to ff.o(i.create_xdir) for create_xdir
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.put_lfn) for put_lfn
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to rt_memclr.o(.text) for __aeabi_memclr
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_close) refers to ff.o(i.validate) for validate
    ff.o(i.f_closedir) refers to ff.o(i.validate) for validate
    ff.o(i.f_getfree) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_getfree) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_getfree) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_getlabel) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_getlabel) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_getlabel) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_getlabel) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_getlabel) refers to ff.o(i.put_utf) for put_utf
    ff.o(i.f_getlabel) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_getlabel) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_gets) refers to ff.o(i.f_read) for f_read
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.fill_last_frag) for fill_last_frag
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_lseek) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.f_lseek) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_mkdir) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ffsystem.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.dir_clear) for dir_clear
    ff.o(i.f_mkdir) refers to aeabi_memset.o(.text) for __aeabi_memset
    ff.o(i.f_mkdir) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkdir) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_mkdir) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.f_mkdir) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_mkfs) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.f_mkfs) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_mkfs) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_mkfs) refers to ffsystem.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkfs) refers to ffunicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.f_mkfs) refers to ff.o(i.xsum32) for xsum32
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_mkfs) refers to ff.o(.data) for FatFs
    ff.o(i.f_mkfs) refers to ff.o(.constdata) for defopt
    ff.o(i.f_mkfs) refers to rt_memclr.o(.text) for __aeabi_memclr
    ff.o(i.f_mkfs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkfs) refers to ff.o(i.st_qword) for st_qword
    ff.o(i.f_mkfs) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_mkfs) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ff.o(i.f_mkfs) refers to ff.o(i.create_partition) for create_partition
    ff.o(i.f_mount) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mount) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_mount) refers to ff.o(.data) for FatFs
    ff.o(i.f_open) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_open) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to ff.o(i.init_alloc_info) for init_alloc_info
    ff.o(i.f_open) refers to rt_memclr.o(.text) for __aeabi_memclr
    ff.o(i.f_open) refers to ffsystem.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_open) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_open) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_open) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_open) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ff.o(i.f_open) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_open) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ff.o(i.f_open) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.f_open) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_open) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_opendir) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_opendir) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.init_alloc_info) for init_alloc_info
    ff.o(i.f_opendir) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_opendir) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_printf) refers to ff.o(i.putc_init) for putc_init
    ff.o(i.f_printf) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_printf) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_putc) refers to ff.o(i.putc_init) for putc_init
    ff.o(i.f_putc) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_putc) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_puts) refers to ff.o(i.putc_init) for putc_init
    ff.o(i.f_puts) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_puts) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ff.o(i.f_read) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_readdir) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_rename) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_rename) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_rename) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ff.o(i.f_rename) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_rename) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_rename) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.f_rename) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_rename) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_rename) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_setlabel) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_setlabel) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ff.o(i.f_setlabel) refers to ff.o(i.tchar2uni) for tchar2uni
    ff.o(i.f_setlabel) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_setlabel) refers to strchr.o(.text) for strchr
    ff.o(i.f_setlabel) refers to aeabi_memset.o(.text) for __aeabi_memset
    ff.o(i.f_setlabel) refers to ffunicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.f_setlabel) refers to ffunicode.o(i.ff_uni2oem) for ff_uni2oem
    ff.o(i.f_setlabel) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_setlabel) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_setlabel) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ff.o(i.f_setlabel) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_setlabel) refers to ff.o(i.dir_alloc) for dir_alloc
    ff.o(i.f_setlabel) refers to rt_memclr.o(.text) for __aeabi_memclr
    ff.o(i.f_setlabel) refers to ff.o(.constdata) for badchr
    ff.o(i.f_stat) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_stat) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_stat) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to ffsystem.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.fill_first_frag) for fill_first_frag
    ff.o(i.f_sync) refers to ff.o(i.fill_last_frag) for fill_last_frag
    ff.o(i.f_sync) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_sync) refers to ff.o(i.load_obj_xdir) for load_obj_xdir
    ff.o(i.f_sync) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_sync) refers to ff.o(i.st_qword) for st_qword
    ff.o(i.f_sync) refers to ff.o(i.store_xdir) for store_xdir
    ff.o(i.f_sync) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_sync) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_sync) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_unlink) refers to ff.o(i.mount_volume) for mount_volume
    ff.o(i.f_unlink) refers to ffsystem.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.init_alloc_info) for init_alloc_info
    ff.o(i.f_unlink) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_unlink) refers to ffsystem.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.f_write) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.fill_first_frag) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.fill_last_frag) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.find_bitmap) refers to ff.o(i.move_window) for move_window
    ff.o(i.find_volume) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.find_volume) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.follow_path) refers to ff.o(i.init_alloc_info) for init_alloc_info
    ff.o(i.follow_path) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.gen_numname) refers to ff.o(i.dbc_1st) for dbc_1st
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fat) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.get_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.get_fat) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ff.o(i.get_fileinfo) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.get_fileinfo) refers to ff.o(i.put_utf) for put_utf
    ff.o(i.get_fileinfo) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.get_fileinfo) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.init_alloc_info) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.init_alloc_info) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.ld_clust) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.load_obj_xdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.load_obj_xdir) refers to ff.o(i.load_xdir) for load_xdir
    ff.o(i.load_xdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.load_xdir) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ff.o(i.load_xdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.load_xdir) refers to ff.o(i.xdir_sum) for xdir_sum
    ff.o(i.load_xdir) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.mount_volume) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.mount_volume) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.mount_volume) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.mount_volume) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.mount_volume) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.mount_volume) refers to ff.o(i.ld_qword) for ld_qword
    ff.o(i.mount_volume) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.mount_volume) refers to ff.o(i.clst2sect) for clst2sect
    ff.o(i.mount_volume) refers to ff.o(i.move_window) for move_window
    ff.o(i.mount_volume) refers to ff.o(.data) for FatFs
    ff.o(i.move_window) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.put_fat) refers to ff.o(i.st_word) for st_word
    ff.o(i.put_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.put_fat) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.put_lfn) refers to ff.o(i.st_word) for st_word
    ff.o(i.put_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.put_utf) refers to ffunicode.o(i.ff_uni2oem) for ff_uni2oem
    ff.o(i.putc_bfd) refers to ff.o(i.f_write) for f_write
    ff.o(i.putc_flush) refers to ff.o(i.f_write) for f_write
    ff.o(i.putc_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.change_bitmap) for change_bitmap
    ff.o(i.st_clust) refers to ff.o(i.st_word) for st_word
    ff.o(i.store_xdir) refers to ff.o(i.xdir_sum) for xdir_sum
    ff.o(i.store_xdir) refers to ff.o(i.st_word) for st_word
    ff.o(i.store_xdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.store_xdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.store_xdir) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ff.o(i.store_xdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.sync_fs) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.sync_fs) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ff.o(i.sync_fs) refers to ff.o(i.st_word) for st_word
    ff.o(i.sync_fs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.sync_fs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync_fs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.sync_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.tchar2uni) refers to ff.o(i.dbc_1st) for dbc_1st
    ff.o(i.tchar2uni) refers to ff.o(i.dbc_2nd) for dbc_2nd
    ff.o(i.tchar2uni) refers to ffunicode.o(i.ff_oem2uni) for ff_oem2uni
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.xname_sum) refers to ffunicode.o(i.ff_wtoupper) for ff_wtoupper
    ffsystem.o(i.ff_memalloc) refers to malloc.o(i.mymalloc) for mymalloc
    ffsystem.o(i.ff_memfree) refers to malloc.o(i.myfree) for myfree
    ffunicode.o(i.ff_oem2uni) refers to ffunicode.o(.constdata) for oem2uni936
    ffunicode.o(i.ff_uni2oem) refers to ffunicode.o(.constdata) for uni2oem936
    ffunicode.o(i.ff_wtoupper) refers to ffunicode.o(.constdata) for cvt1
    exfuns.o(i.exfuns_file_copy) refers to malloc.o(i.mymalloc) for mymalloc
    exfuns.o(i.exfuns_file_copy) refers to ff.o(i.f_open) for f_open
    exfuns.o(i.exfuns_file_copy) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    exfuns.o(i.exfuns_file_copy) refers to ff.o(i.f_read) for f_read
    exfuns.o(i.exfuns_file_copy) refers to ff.o(i.f_write) for f_write
    exfuns.o(i.exfuns_file_copy) refers to ff.o(i.f_close) for f_close
    exfuns.o(i.exfuns_file_copy) refers to malloc.o(i.myfree) for myfree
    exfuns.o(i.exfuns_file_type) refers to strcpy.o(.text) for strcpy
    exfuns.o(i.exfuns_file_type) refers to exfuns.o(i.exfuns_char_upper) for exfuns_char_upper
    exfuns.o(i.exfuns_file_type) refers to strcmpv7m.o(.text) for strcmp
    exfuns.o(i.exfuns_file_type) refers to exfuns.o(.constdata) for FILE_TYPE_TBL
    exfuns.o(i.exfuns_folder_copy) refers to malloc.o(i.mymalloc) for mymalloc
    exfuns.o(i.exfuns_folder_copy) refers to strcat.o(.text) for strcat
    exfuns.o(i.exfuns_folder_copy) refers to ff.o(i.f_opendir) for f_opendir
    exfuns.o(i.exfuns_folder_copy) refers to exfuns.o(i.exfuns_get_src_dname) for exfuns_get_src_dname
    exfuns.o(i.exfuns_folder_copy) refers to strlen.o(.text) for strlen
    exfuns.o(i.exfuns_folder_copy) refers to ff.o(i.f_mkdir) for f_mkdir
    exfuns.o(i.exfuns_folder_copy) refers to ff.o(i.f_readdir) for f_readdir
    exfuns.o(i.exfuns_folder_copy) refers to exfuns.o(i.exfuns_file_copy) for exfuns_file_copy
    exfuns.o(i.exfuns_folder_copy) refers to malloc.o(i.myfree) for myfree
    exfuns.o(i.exfuns_get_folder_size) refers to malloc.o(i.mymalloc) for mymalloc
    exfuns.o(i.exfuns_get_folder_size) refers to strcat.o(.text) for strcat
    exfuns.o(i.exfuns_get_folder_size) refers to ff.o(i.f_opendir) for f_opendir
    exfuns.o(i.exfuns_get_folder_size) refers to ff.o(i.f_readdir) for f_readdir
    exfuns.o(i.exfuns_get_folder_size) refers to strlen.o(.text) for strlen
    exfuns.o(i.exfuns_get_folder_size) refers to malloc.o(i.myfree) for myfree
    exfuns.o(i.exfuns_get_free) refers to ff.o(i.f_getfree) for f_getfree
    exfuns.o(i.exfuns_init) refers to malloc.o(i.mymalloc) for mymalloc
    exfuns.o(i.exfuns_init) refers to fattester.o(i.mf_init) for mf_init
    exfuns.o(i.exfuns_init) refers to exfuns.o(.data) for fs
    exfuns.o(.constdata) refers to exfuns.o(.conststring) for .conststring
    fattester.o(i.mf_close) refers to ff.o(i.f_close) for f_close
    fattester.o(i.mf_close) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_closedir) refers to ff.o(i.f_closedir) for f_closedir
    fattester.o(i.mf_closedir) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_fmkfs) refers to ff.o(i.f_mkfs) for f_mkfs
    fattester.o(i.mf_fmkfs) refers to fattester.o(.constdata) for .constdata
    fattester.o(i.mf_free) refers to malloc.o(i.myfree) for myfree
    fattester.o(i.mf_free) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_getlabel) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_getlabel) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_getlabel) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_getlabel) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fattester.o(i.mf_getlabel) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    fattester.o(i.mf_getlabel) refers to ff.o(i.f_getlabel) for f_getlabel
    fattester.o(i.mf_getlabel) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_gets) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_gets) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_gets) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_gets) refers to ff.o(i.f_gets) for f_gets
    fattester.o(i.mf_gets) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_gets) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_init) refers to malloc.o(i.mymalloc) for mymalloc
    fattester.o(i.mf_init) refers to fattester.o(i.mf_free) for mf_free
    fattester.o(i.mf_init) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_lseek) refers to ff.o(i.f_lseek) for f_lseek
    fattester.o(i.mf_lseek) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_mkdir) refers to ff.o(i.f_mkdir) for f_mkdir
    fattester.o(i.mf_mount) refers to ff.o(i.f_mount) for f_mount
    fattester.o(i.mf_mount) refers to exfuns.o(.data) for fs
    fattester.o(i.mf_open) refers to ff.o(i.f_open) for f_open
    fattester.o(i.mf_open) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_opendir) refers to ff.o(i.f_opendir) for f_opendir
    fattester.o(i.mf_opendir) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_putc) refers to ff.o(i.f_putc) for f_putc
    fattester.o(i.mf_putc) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_puts) refers to ff.o(i.f_puts) for f_puts
    fattester.o(i.mf_puts) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_read) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_read) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fattester.o(i.mf_read) refers to _printf_dec.o(.text) for _printf_int_dec
    fattester.o(i.mf_read) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    fattester.o(i.mf_read) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_read) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_read) refers to ff.o(i.f_read) for f_read
    fattester.o(i.mf_read) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_readdir) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_readdir) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fattester.o(i.mf_readdir) refers to _printf_dec.o(.text) for _printf_int_dec
    fattester.o(i.mf_readdir) refers to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    fattester.o(i.mf_readdir) refers to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    fattester.o(i.mf_readdir) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_readdir) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_readdir) refers to ff.o(i.f_readdir) for f_readdir
    fattester.o(i.mf_readdir) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_readdir) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_rename) refers to ff.o(i.f_rename) for f_rename
    fattester.o(i.mf_scan_files) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_scan_files) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_scan_files) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_scan_files) refers to ff.o(i.f_opendir) for f_opendir
    fattester.o(i.mf_scan_files) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_scan_files) refers to ff.o(i.f_readdir) for f_readdir
    fattester.o(i.mf_scan_files) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_setlabel) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_setlabel) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    fattester.o(i.mf_setlabel) refers to _printf_str.o(.text) for _printf_str
    fattester.o(i.mf_setlabel) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    fattester.o(i.mf_setlabel) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    fattester.o(i.mf_setlabel) refers to ff.o(i.f_setlabel) for f_setlabel
    fattester.o(i.mf_setlabel) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_showfree) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_showfree) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fattester.o(i.mf_showfree) refers to _printf_dec.o(.text) for _printf_int_dec
    fattester.o(i.mf_showfree) refers to ff.o(i.f_getfree) for f_getfree
    fattester.o(i.mf_showfree) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_size) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_tell) refers to fattester.o(.bss) for fattester
    fattester.o(i.mf_unlink) refers to ff.o(i.f_unlink) for f_unlink
    fattester.o(i.mf_write) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    fattester.o(i.mf_write) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    fattester.o(i.mf_write) refers to _printf_dec.o(.text) for _printf_int_dec
    fattester.o(i.mf_write) refers to noretval__2printf.o(.text) for __2printf
    fattester.o(i.mf_write) refers to ff.o(i.f_write) for f_write
    fattester.o(i.mf_write) refers to fattester.o(.bss) for fattester
    malloc.o(i.my_mem_free) refers to malloc.o(.data) for mallco_dev
    malloc.o(i.my_mem_free) refers to malloc.o(.constdata) for memsize
    malloc.o(i.my_mem_init) refers to malloc.o(i.my_mem_set) for my_mem_set
    malloc.o(i.my_mem_init) refers to malloc.o(.constdata) for memtblsize
    malloc.o(i.my_mem_init) refers to malloc.o(.data) for mallco_dev
    malloc.o(i.my_mem_malloc) refers to malloc.o(.data) for mallco_dev
    malloc.o(i.my_mem_malloc) refers to malloc.o(.constdata) for memblksize
    malloc.o(i.my_mem_perused) refers to malloc.o(.data) for mallco_dev
    malloc.o(i.my_mem_perused) refers to malloc.o(.constdata) for memtblsize
    malloc.o(i.myfree) refers to malloc.o(i.my_mem_free) for my_mem_free
    malloc.o(i.myfree) refers to malloc.o(.data) for mallco_dev
    malloc.o(i.mymalloc) refers to malloc.o(i.my_mem_malloc) for my_mem_malloc
    malloc.o(i.mymalloc) refers to malloc.o(.data) for mallco_dev
    malloc.o(i.myrealloc) refers to malloc.o(i.my_mem_malloc) for my_mem_malloc
    malloc.o(i.myrealloc) refers to malloc.o(i.my_mem_copy) for my_mem_copy
    malloc.o(i.myrealloc) refers to malloc.o(i.myfree) for myfree
    malloc.o(i.myrealloc) refers to malloc.o(.data) for mallco_dev
    malloc.o(.data) refers to malloc.o(i.my_mem_init) for my_mem_init
    malloc.o(.data) refers to malloc.o(i.my_mem_perused) for my_mem_perused
    malloc.o(.data) refers to malloc.o(.bss) for mem1base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x10000000) for mem2base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x1000F000) for mem2mapbase
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    mktime.o(.text) refers to _monlen.o(.constdata) for _monlen
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    atof.o(i.__hardfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _scanf.o(.text) refers (Weak) to scanf1.o(x$fpl$scanf1) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_gd32f450_470.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to usart.o(i._ttywrch) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to usart.o(i._sys_command_string) for _sys_command_string
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.get_system_time), (44 bytes).
    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rrx_text), (6 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing system_gd32f4xx.o(.data), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.sys_intx_disable), (4 bytes).
    Removing sys.o(i.sys_intx_enable), (4 bytes).
    Removing sys.o(i.sys_msr_msp), (10 bytes).
    Removing sys.o(i.sys_soft_reset), (44 bytes).
    Removing sys.o(i.sys_tick_get), (8 bytes).
    Removing sys.o(i.sys_wfi_set), (4 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i._sys_command_string), (6 bytes).
    Removing usart.o(i._ttywrch), (4 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (48 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (12 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (38 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (40 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_mode_init), (344 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (148 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_double_bank_select), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (84 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_reset), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_set), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (108 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (52 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing myiic.o(.rev16_text), (4 bytes).
    Removing myiic.o(.revsh_text), (4 bytes).
    Removing myiic.o(.rrx_text), (6 bytes).
    Removing myiic.o(i.iic_ack), (84 bytes).
    Removing myiic.o(i.iic_nack), (64 bytes).
    Removing myiic.o(i.iic_read_byte), (96 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Show_Font), (140 bytes).
    Removing oled.o(i.oled_display_off), (28 bytes).
    Removing oled.o(i.oled_display_on), (28 bytes).
    Removing oled.o(i.oled_fill), (58 bytes).
    Removing oled.o(i.oled_pow), (22 bytes).
    Removing oled.o(i.oled_show_num), (148 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing fmc.o(.rev16_text), (4 bytes).
    Removing fmc.o(.revsh_text), (4 bytes).
    Removing fmc.o(.rrx_text), (6 bytes).
    Removing fmc.o(i.fmc_erase_sector), (32 bytes).
    Removing fmc.o(i.fmc_read_32bit_data), (24 bytes).
    Removing fmc.o(i.fmc_sector_get), (352 bytes).
    Removing fmc.o(i.fmc_write_32bit_data), (172 bytes).
    Removing fmc.o(i.sector_name_to_number), (48 bytes).
    Removing fmc.o(i.test_read), (6 bytes).
    Removing fmc.o(i.test_write), (16 bytes).
    Removing norflash.o(.rev16_text), (4 bytes).
    Removing norflash.o(.revsh_text), (4 bytes).
    Removing norflash.o(.rrx_text), (6 bytes).
    Removing norflash.o(i.norflash_erase_chip), (56 bytes).
    Removing norflash.o(i.norflash_write_disable), (44 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(.rrx_text), (6 bytes).
    Removing rtc.o(i.rtc_get_week), (128 bytes).
    Removing rtc.o(i.rtc_set_alarma), (136 bytes).
    Removing rtc.o(i.rtc_set_wakeup), (74 bytes).
    Removing rtc.o(.constdata), (12 bytes).
    Removing sd_conf.o(.rev16_text), (4 bytes).
    Removing sd_conf.o(.revsh_text), (4 bytes).
    Removing sd_conf.o(.rrx_text), (6 bytes).
    Removing sd_conf.o(i.card_info_get), (980 bytes).
    Removing sd_conf.o(i.sd_read_udisk), (58 bytes).
    Removing sd_conf.o(i.sd_write_udisk), (58 bytes).
    Removing sdio_sdcard.o(.rev16_text), (4 bytes).
    Removing sdio_sdcard.o(.revsh_text), (4 bytes).
    Removing sdio_sdcard.o(.rrx_text), (6 bytes).
    Removing sdio_sdcard.o(i.sd_erase), (324 bytes).
    Removing sdio_sdcard.o(i.sd_power_off), (14 bytes).
    Removing sdio_sdcard.o(i.sd_sdstatus_get), (384 bytes).
    Removing sdio_sdcard.o(i.sd_transfer_state_get), (20 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing timer.o(.rrx_text), (6 bytes).
    Removing timer.o(i.timerx_disable), (16 bytes).
    Removing timer.o(i.timerx_enable), (16 bytes).
    Removing timer.o(i.timerx_set_period), (36 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing diskio.o(.rrx_text), (6 bytes).
    Removing ff.o(i.create_partition), (346 bytes).
    Removing ff.o(i.dir_remove), (114 bytes).
    Removing ff.o(i.f_closedir), (24 bytes).
    Removing ff.o(i.f_getfree), (348 bytes).
    Removing ff.o(i.f_getlabel), (316 bytes).
    Removing ff.o(i.f_lseek), (1134 bytes).
    Removing ff.o(i.f_mkfs), (3580 bytes).
    Removing ff.o(i.f_opendir), (184 bytes).
    Removing ff.o(i.f_printf), (692 bytes).
    Removing ff.o(i.f_putc), (34 bytes).
    Removing ff.o(i.f_puts), (44 bytes).
    Removing ff.o(i.f_readdir), (112 bytes).
    Removing ff.o(i.f_rename), (516 bytes).
    Removing ff.o(i.f_setlabel), (492 bytes).
    Removing ff.o(i.f_stat), (96 bytes).
    Removing ff.o(i.f_truncate), (214 bytes).
    Removing ff.o(i.f_unlink), (252 bytes).
    Removing ff.o(i.get_fileinfo), (634 bytes).
    Removing ff.o(i.put_utf), (66 bytes).
    Removing ff.o(i.putc_bfd), (68 bytes).
    Removing ff.o(i.putc_flush), (44 bytes).
    Removing ff.o(i.putc_init), (18 bytes).
    Removing ff.o(i.xsum32), (26 bytes).
    Removing ffsystem.o(.rev16_text), (4 bytes).
    Removing ffsystem.o(.revsh_text), (4 bytes).
    Removing ffsystem.o(.rrx_text), (6 bytes).
    Removing exfuns.o(.rev16_text), (4 bytes).
    Removing exfuns.o(.revsh_text), (4 bytes).
    Removing exfuns.o(.rrx_text), (6 bytes).
    Removing exfuns.o(i.exfuns_char_upper), (26 bytes).
    Removing exfuns.o(i.exfuns_file_copy), (396 bytes).
    Removing exfuns.o(i.exfuns_file_type), (192 bytes).
    Removing exfuns.o(i.exfuns_folder_copy), (480 bytes).
    Removing exfuns.o(i.exfuns_get_folder_size), (232 bytes).
    Removing exfuns.o(i.exfuns_get_free), (74 bytes).
    Removing exfuns.o(i.exfuns_get_src_dname), (46 bytes).
    Removing exfuns.o(i.exfuns_init), (68 bytes).
    Removing exfuns.o(.constdata), (196 bytes).
    Removing exfuns.o(.conststring), (84 bytes).
    Removing exfuns.o(.data), (8 bytes).
    Removing fattester.o(.rev16_text), (4 bytes).
    Removing fattester.o(.revsh_text), (4 bytes).
    Removing fattester.o(.rrx_text), (6 bytes).
    Removing fattester.o(i.mf_close), (20 bytes).
    Removing fattester.o(i.mf_closedir), (16 bytes).
    Removing fattester.o(i.mf_fmkfs), (44 bytes).
    Removing fattester.o(i.mf_free), (32 bytes).
    Removing fattester.o(i.mf_getlabel), (132 bytes).
    Removing fattester.o(i.mf_gets), (72 bytes).
    Removing fattester.o(i.mf_init), (64 bytes).
    Removing fattester.o(i.mf_lseek), (24 bytes).
    Removing fattester.o(i.mf_mkdir), (12 bytes).
    Removing fattester.o(i.mf_mount), (24 bytes).
    Removing fattester.o(i.mf_open), (28 bytes).
    Removing fattester.o(i.mf_opendir), (20 bytes).
    Removing fattester.o(i.mf_putc), (24 bytes).
    Removing fattester.o(i.mf_puts), (24 bytes).
    Removing fattester.o(i.mf_read), (348 bytes).
    Removing fattester.o(i.mf_readdir), (628 bytes).
    Removing fattester.o(i.mf_rename), (16 bytes).
    Removing fattester.o(i.mf_scan_files), (100 bytes).
    Removing fattester.o(i.mf_setlabel), (92 bytes).
    Removing fattester.o(i.mf_showfree), (168 bytes).
    Removing fattester.o(i.mf_size), (12 bytes).
    Removing fattester.o(i.mf_tell), (12 bytes).
    Removing fattester.o(i.mf_unlink), (12 bytes).
    Removing fattester.o(i.mf_write), (196 bytes).
    Removing fattester.o(.bss), (384 bytes).
    Removing fattester.o(.constdata), (16 bytes).
    Removing malloc.o(.rev16_text), (4 bytes).
    Removing malloc.o(.revsh_text), (4 bytes).
    Removing malloc.o(.rrx_text), (6 bytes).
    Removing malloc.o(i.my_mem_copy), (26 bytes).
    Removing malloc.o(i.myrealloc), (68 bytes).

1007 unused section(s) (total 67576 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcat.o ABSOLUTE
    ../clib/time.c                           0x00000000   Number         0  _monlen.o ABSOLUTE
    ../clib/time.c                           0x00000000   Number         0  mktime.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\..\Drivers\BSP\ADC\adc.c              0x00000000   Number         0  adc.o ABSOLUTE
    ..\..\Drivers\BSP\FMC\fmc.c              0x00000000   Number         0  fmc.o ABSOLUTE
    ..\..\Drivers\BSP\IIC\myiic.c            0x00000000   Number         0  myiic.o ABSOLUTE
    ..\..\Drivers\BSP\KEY\key.c              0x00000000   Number         0  key.o ABSOLUTE
    ..\..\Drivers\BSP\LED\led.c              0x00000000   Number         0  led.o ABSOLUTE
    ..\..\Drivers\BSP\NORFLASH\norflash.c    0x00000000   Number         0  norflash.o ABSOLUTE
    ..\..\Drivers\BSP\OLED\oled.c            0x00000000   Number         0  oled.o ABSOLUTE
    ..\..\Drivers\BSP\RTC\rtc.c              0x00000000   Number         0  rtc.o ABSOLUTE
    ..\..\Drivers\BSP\SDIO\sd_conf.c         0x00000000   Number         0  sd_conf.o ABSOLUTE
    ..\..\Drivers\BSP\SDIO\sdio_sdcard.c     0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\..\Drivers\BSP\SPI\spi.c              0x00000000   Number         0  spi.o ABSOLUTE
    ..\..\Drivers\BSP\TIMER\timer.c          0x00000000   Number         0  timer.o ABSOLUTE
    ..\..\Drivers\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\..\Drivers\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\..\Drivers\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\..\Drivers\SYSTEM\delay\delay.c       0x00000000   Number         0  delay.o ABSOLUTE
    ..\..\Drivers\SYSTEM\sys\sys.c           0x00000000   Number         0  sys.o ABSOLUTE
    ..\..\Drivers\SYSTEM\usart\usart.c       0x00000000   Number         0  usart.o ABSOLUTE
    ..\..\Middlewares\FATFS\exfuns\exfuns.c  0x00000000   Number         0  exfuns.o ABSOLUTE
    ..\..\Middlewares\FATFS\exfuns\fattester.c 0x00000000   Number         0  fattester.o ABSOLUTE
    ..\..\Middlewares\FATFS\source\diskio.c  0x00000000   Number         0  diskio.o ABSOLUTE
    ..\..\Middlewares\FATFS\source\ff.c      0x00000000   Number         0  ff.o ABSOLUTE
    ..\..\Middlewares\FATFS\source\ffsystem.c 0x00000000   Number         0  ffsystem.o ABSOLUTE
    ..\..\Middlewares\FATFS\source\ffunicode.c 0x00000000   Number         0  ffunicode.o ABSOLUTE
    ..\..\Middlewares\MALLOC\malloc.c        0x00000000   Number         0  malloc.o ABSOLUTE
    ..\..\User\gd32f4xx_it.c                 0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\..\User\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\ADC\\adc.c         0x00000000   Number         0  adc.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\FMC\\fmc.c         0x00000000   Number         0  fmc.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\IIC\\myiic.c       0x00000000   Number         0  myiic.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\KEY\\key.c         0x00000000   Number         0  key.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\LED\\led.c         0x00000000   Number         0  led.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\NORFLASH\\norflash.c 0x00000000   Number         0  norflash.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\OLED\\oled.c       0x00000000   Number         0  oled.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\RTC\\rtc.c         0x00000000   Number         0  rtc.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\SDIO\\sd_conf.c    0x00000000   Number         0  sd_conf.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\SDIO\\sdio_sdcard.c 0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\SPI\\spi.c         0x00000000   Number         0  spi.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\TIMER\\timer.c     0x00000000   Number         0  timer.o ABSOLUTE
    ..\\..\\Drivers\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\..\\Drivers\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\delay\\delay.c  0x00000000   Number         0  delay.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\sys\\sys.c      0x00000000   Number         0  sys.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\usart\\usart.c  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\..\\Middlewares\\FATFS\\exfuns\\exfuns.c 0x00000000   Number         0  exfuns.o ABSOLUTE
    ..\\..\\Middlewares\\FATFS\\exfuns\\fattester.c 0x00000000   Number         0  fattester.o ABSOLUTE
    ..\\..\\Middlewares\\FATFS\\source\\diskio.c 0x00000000   Number         0  diskio.o ABSOLUTE
    ..\\..\\Middlewares\\FATFS\\source\\ffsystem.c 0x00000000   Number         0  ffsystem.o ABSOLUTE
    ..\\..\\Middlewares\\MALLOC\\malloc.c    0x00000000   Number         0  malloc.o ABSOLUTE
    ..\\..\\User\\gd32f4xx_it.c              0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\..\\User\\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001e8   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000204   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000220   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x08000220   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000226   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800022c   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000232   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x08000238   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800023e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000242   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000244   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000248   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800024e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x0800024e   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x0800025a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800025a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x0800025a   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000264   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000266   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000268   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000268   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800026a   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800026a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800026a   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000270   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000270   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000274   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000274   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800027c   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800027e   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800027e   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000282   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000288   Section       64  startup_gd32f450_470.o(.text)
    $v0                                      0x08000288   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x080002c8   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080002ca   Section      238  lludivv7m.o(.text)
    .text                                    0x080003b8   Section        0  noretval__2printf.o(.text)
    .text                                    0x080003d0   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x080003f8   Section        0  _printf_pad.o(.text)
    .text                                    0x08000446   Section        0  _printf_str.o(.text)
    .text                                    0x08000498   Section        0  _printf_dec.o(.text)
    .text                                    0x08000510   Section        0  _printf_hex_int.o(.text)
    .text                                    0x08000568   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080006f0   Section        0  __0sscanf.o(.text)
    .text                                    0x0800072c   Section        0  _scanf_int.o(.text)
    .text                                    0x08000878   Section        0  strchr.o(.text)
    .text                                    0x0800088c   Section        0  strstr.o(.text)
    .text                                    0x080008b0   Section        0  memcmp.o(.text)
    .text                                    0x08000908   Section        0  strlen.o(.text)
    .text                                    0x08000946   Section        0  strncmp.o(.text)
    .text                                    0x080009dc   Section        0  strcat.o(.text)
    .text                                    0x080009f4   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000a7e   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000ae2   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000af2   Section       68  rt_memclr.o(.text)
    .text                                    0x08000b36   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000b84   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000c04   Section        0  mktime.o(.text)
    _tm_carry                                0x08000c05   Thumb Code    62  mktime.o(.text)
    .text                                    0x08000db0   Section        0  heapauxi.o(.text)
    .text                                    0x08000db6   Section        2  use_no_semi.o(.text)
    .text                                    0x08000db8   Section      138  lludiv10.o(.text)
    .text                                    0x08000e42   Section        0  _rserrno.o(.text)
    .text                                    0x08000e58   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000f0a   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000f0d   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001328   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08001329   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08001358   Section        0  _sputc.o(.text)
    .text                                    0x08001362   Section        0  _printf_char.o(.text)
    .text                                    0x08001390   Section        0  _printf_char_file.o(.text)
    .text                                    0x080013b4   Section        0  _chval.o(.text)
    .text                                    0x080013d0   Section        0  scanf_fp.o(.text)
    _fp_value                                0x080013d1   Thumb Code   588  scanf_fp.o(.text)
    .text                                    0x080018c8   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x080018c9   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x080018f4   Section        0  _sgetc.o(.text)
    .text                                    0x08001934   Section        0  strtod.o(.text)
    _local_sscanf                            0x08001935   Thumb Code    60  strtod.o(.text)
    .text                                    0x080019d8   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080019e0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080019e8   Section        0  isspace.o(.text)
    .text                                    0x080019fc   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001a7c   Section        0  _scanf.o(.text)
    .text                                    0x08001df0   Section        0  bigflt0.o(.text)
    .text                                    0x08001ed4   Section        0  ferror.o(.text)
    .text                                    0x08001edc   Section        8  libspace.o(.text)
    .text                                    0x08001ee4   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001f30   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001f40   Section        0  scanf_hexfp.o(.text)
    .text                                    0x08002260   Section        0  scanf_infnan.o(.text)
    .text                                    0x08002394   Section        0  exit.o(.text)
    .text                                    0x080023a6   Section       38  llshl.o(.text)
    CL$$btod_d2e                             0x080023cc   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x0800240a   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08002450   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080024b0   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x080027e8   Section      132  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x0800286c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002948   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x08002972   Section       42  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x0800299c   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x080029c6   Section       42  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x080029f0   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x08002c34   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08002c38   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.HardFault_Handler                      0x08002c3c   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08002c40   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08002c44   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08002c48   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.RTC_Alarm_IRQHandler                   0x08002c4c   Section        0  rtc.o(i.RTC_Alarm_IRQHandler)
    i.RTC_WKUP_IRQHandler                    0x08002c7c   Section        0  rtc.o(i.RTC_WKUP_IRQHandler)
    i.SDIO_IRQHandler                        0x08002cac   Section        0  sd_conf.o(i.SDIO_IRQHandler)
    i.SVC_Handler                            0x08002cb4   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08002cb8   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08002cbc   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TIMER6_IRQHandler                      0x08002d90   Section        0  main.o(i.TIMER6_IRQHandler)
    i.USART0_IRQHandler                      0x08002e10   Section        0  usart.o(i.USART0_IRQHandler)
    i.UsageFault_Handler                     0x08002e98   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08002e9c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp___mathlib_tofloat             0x08002ed0   Section        0  narrow.o(i.__hardfp___mathlib_tofloat)
    i.__hardfp_atof                          0x08002fc8   Section        0  atof.o(i.__hardfp_atof)
    i.__hardfp_ldexp                         0x08003000   Section        0  ldexp.o(i.__hardfp_ldexp)
    i.__mathlib_dbl_overflow                 0x080030d0   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x080030f0   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_narrow                       0x08003110   Section        0  narrow.o(i.__mathlib_narrow)
    i.__support_ldexp                        0x08003122   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x08003136   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x08003144   Section        0  usart.o(i._sys_exit)
    i.adc_calibration_enable                 0x08003148   Section        0  gd32f4xx_adc.o(i.adc_calibration_enable)
    i.adc_channel_length_config              0x08003172   Section        0  gd32f4xx_adc.o(i.adc_channel_length_config)
    i.adc_clock_config                       0x080031c4   Section        0  gd32f4xx_adc.o(i.adc_clock_config)
    i.adc_data_alignment_config              0x080031e8   Section        0  gd32f4xx_adc.o(i.adc_data_alignment_config)
    i.adc_deinit                             0x080031fe   Section        0  gd32f4xx_adc.o(i.adc_deinit)
    i.adc_enable                             0x08003212   Section        0  gd32f4xx_adc.o(i.adc_enable)
    i.adc_external_trigger_config            0x08003224   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_config)
    i.adc_flag_clear                         0x08003258   Section        0  gd32f4xx_adc.o(i.adc_flag_clear)
    i.adc_flag_get                           0x08003260   Section        0  gd32f4xx_adc.o(i.adc_flag_get)
    i.adc_get_result                         0x08003270   Section        0  adc.o(i.adc_get_result)
    i.adc_get_result_average                 0x080032ac   Section        0  adc.o(i.adc_get_result_average)
    i.adc_init                               0x080032d8   Section        0  adc.o(i.adc_init)
    i.adc_resolution_config                  0x08003360   Section        0  gd32f4xx_adc.o(i.adc_resolution_config)
    i.adc_routine_channel_config             0x08003370   Section        0  gd32f4xx_adc.o(i.adc_routine_channel_config)
    i.adc_routine_data_read                  0x0800341c   Section        0  gd32f4xx_adc.o(i.adc_routine_data_read)
    i.adc_software_trigger_enable            0x08003424   Section        0  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    i.adc_special_function_config            0x08003448   Section        0  gd32f4xx_adc.o(i.adc_special_function_config)
    i.adc_sync_mode_config                   0x080034a4   Section        0  gd32f4xx_adc.o(i.adc_sync_mode_config)
    i.adjust_sample_cycle                    0x080034c8   Section        0  main.o(i.adjust_sample_cycle)
    i.change_bitmap                          0x08003594   Section        0  ff.o(i.change_bitmap)
    change_bitmap                            0x08003595   Thumb Code   138  ff.o(i.change_bitmap)
    i.check_fs                               0x08003620   Section        0  ff.o(i.check_fs)
    check_fs                                 0x08003621   Thumb Code   236  ff.o(i.check_fs)
    i.clmt_clust                             0x08003724   Section        0  ff.o(i.clmt_clust)
    clmt_clust                               0x08003725   Thumb Code    98  ff.o(i.clmt_clust)
    i.clst2sect                              0x08003786   Section        0  ff.o(i.clst2sect)
    clst2sect                                0x08003787   Thumb Code    26  ff.o(i.clst2sect)
    i.cmdsent_error_check                    0x080037a0   Section        0  sdio_sdcard.o(i.cmdsent_error_check)
    cmdsent_error_check                      0x080037a1   Thumb Code    50  sdio_sdcard.o(i.cmdsent_error_check)
    i.cmp_lfn                                0x080037dc   Section        0  ff.o(i.cmp_lfn)
    cmp_lfn                                  0x080037dd   Thumb Code   148  ff.o(i.cmp_lfn)
    i.create_chain                           0x08003874   Section        0  ff.o(i.create_chain)
    create_chain                             0x08003875   Thumb Code   460  ff.o(i.create_chain)
    i.create_name                            0x08003a40   Section        0  ff.o(i.create_name)
    create_name                              0x08003a41   Thumb Code   584  ff.o(i.create_name)
    i.create_xdir                            0x08003c9c   Section        0  ff.o(i.create_xdir)
    create_xdir                              0x08003c9d   Thumb Code   144  ff.o(i.create_xdir)
    i.datetime_to_unix                       0x08003d2c   Section        0  main.o(i.datetime_to_unix)
    i.dbc_1st                                0x08003d60   Section        0  ff.o(i.dbc_1st)
    dbc_1st                                  0x08003d61   Thumb Code    46  ff.o(i.dbc_1st)
    i.dbc_2nd                                0x08003d94   Section        0  ff.o(i.dbc_2nd)
    dbc_2nd                                  0x08003d95   Thumb Code    66  ff.o(i.dbc_2nd)
    i.delay_init                             0x08003ddc   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08003e08   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08003e1c   Section        0  delay.o(i.delay_us)
    i.dir_alloc                              0x08003e64   Section        0  ff.o(i.dir_alloc)
    dir_alloc                                0x08003e65   Thumb Code   124  ff.o(i.dir_alloc)
    i.dir_clear                              0x08003ee0   Section        0  ff.o(i.dir_clear)
    dir_clear                                0x08003ee1   Thumb Code   194  ff.o(i.dir_clear)
    i.dir_find                               0x08003fa2   Section        0  ff.o(i.dir_find)
    dir_find                                 0x08003fa3   Thumb Code   424  ff.o(i.dir_find)
    i.dir_next                               0x0800414a   Section        0  ff.o(i.dir_next)
    dir_next                                 0x0800414b   Thumb Code   224  ff.o(i.dir_next)
    i.dir_read                               0x0800422a   Section        0  ff.o(i.dir_read)
    dir_read                                 0x0800422b   Thumb Code   258  ff.o(i.dir_read)
    i.dir_register                           0x0800432c   Section        0  ff.o(i.dir_register)
    dir_register                             0x0800432d   Thumb Code   554  ff.o(i.dir_register)
    i.dir_sdi                                0x08004556   Section        0  ff.o(i.dir_sdi)
    i.disk_initialize                        0x08004600   Section        0  diskio.o(i.disk_initialize)
    i.disk_ioctl                             0x08004630   Section        0  diskio.o(i.disk_ioctl)
    i.disk_read                              0x080046c0   Section        0  diskio.o(i.disk_read)
    i.disk_status                            0x0800473e   Section        0  diskio.o(i.disk_status)
    i.disk_write                             0x08004744   Section        0  diskio.o(i.disk_write)
    i.dma_channel_disable                    0x080047c2   Section        0  gd32f4xx_dma.o(i.dma_channel_disable)
    i.dma_channel_enable                     0x080047e2   Section        0  gd32f4xx_dma.o(i.dma_channel_enable)
    i.dma_channel_subperipheral_select       0x08004802   Section        0  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    i.dma_deinit                             0x08004828   Section        0  gd32f4xx_dma.o(i.dma_deinit)
    i.dma_flag_clear                         0x080048ce   Section        0  gd32f4xx_dma.o(i.dma_flag_clear)
    i.dma_flag_get                           0x0800490c   Section        0  gd32f4xx_dma.o(i.dma_flag_get)
    i.dma_flow_controller_config             0x08004958   Section        0  gd32f4xx_dma.o(i.dma_flow_controller_config)
    i.dma_multi_data_mode_init               0x08004998   Section        0  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    i.dma_receive_config                     0x08004afc   Section        0  sdio_sdcard.o(i.dma_receive_config)
    dma_receive_config                       0x08004afd   Thumb Code   170  sdio_sdcard.o(i.dma_receive_config)
    i.dma_transfer_config                    0x08004bb0   Section        0  sdio_sdcard.o(i.dma_transfer_config)
    dma_transfer_config                      0x08004bb1   Thumb Code   172  sdio_sdcard.o(i.dma_transfer_config)
    i.encode_voltage                         0x08004c64   Section        0  main.o(i.encode_voltage)
    i.exti_flag_clear                        0x08004cb4   Section        0  gd32f4xx_exti.o(i.exti_flag_clear)
    i.f_close                                0x08004cc0   Section        0  ff.o(i.f_close)
    i.f_gets                                 0x08004ce6   Section        0  ff.o(i.f_gets)
    i.f_mkdir                                0x08004d36   Section        0  ff.o(i.f_mkdir)
    i.f_mount                                0x08004ed8   Section        0  ff.o(i.f_mount)
    i.f_open                                 0x08004f2c   Section        0  ff.o(i.f_open)
    i.f_read                                 0x0800523e   Section        0  ff.o(i.f_read)
    i.f_sync                                 0x08005466   Section        0  ff.o(i.f_sync)
    i.f_write                                0x0800560e   Section        0  ff.o(i.f_write)
    i.ff_memalloc                            0x08005892   Section        0  ffsystem.o(i.ff_memalloc)
    i.ff_memfree                             0x080058a0   Section        0  ffsystem.o(i.ff_memfree)
    i.ff_oem2uni                             0x080058b0   Section        0  ffunicode.o(i.ff_oem2uni)
    i.ff_uni2oem                             0x08005918   Section        0  ffunicode.o(i.ff_uni2oem)
    i.ff_wtoupper                            0x0800598c   Section        0  ffunicode.o(i.ff_wtoupper)
    i.fill_first_frag                        0x08005a3c   Section        0  ff.o(i.fill_first_frag)
    fill_first_frag                          0x08005a3d   Thumb Code    54  ff.o(i.fill_first_frag)
    i.fill_last_frag                         0x08005a72   Section        0  ff.o(i.fill_last_frag)
    fill_last_frag                           0x08005a73   Thumb Code    68  ff.o(i.fill_last_frag)
    i.find_bitmap                            0x08005ab6   Section        0  ff.o(i.find_bitmap)
    find_bitmap                              0x08005ab7   Thumb Code   164  ff.o(i.find_bitmap)
    i.find_volume                            0x08005b5a   Section        0  ff.o(i.find_volume)
    find_volume                              0x08005b5b   Thumb Code   120  ff.o(i.find_volume)
    i.follow_path                            0x08005bd2   Section        0  ff.o(i.follow_path)
    follow_path                              0x08005bd3   Thumb Code   196  ff.o(i.follow_path)
    i.fputc                                  0x08005c98   Section        0  usart.o(i.fputc)
    i.frexp                                  0x08005cc0   Section        0  frexp.o(i.frexp)
    i.gen_numname                            0x08005d4c   Section        0  ff.o(i.gen_numname)
    gen_numname                              0x08005d4d   Thumb Code   206  ff.o(i.gen_numname)
    i.get_current_time                       0x08005e20   Section        0  main.o(i.get_current_time)
    i.get_fat                                0x08005e3e   Section        0  ff.o(i.get_fat)
    get_fat                                  0x08005e3f   Thumb Code   404  ff.o(i.get_fat)
    i.get_fattime                            0x08005fd2   Section        0  ffsystem.o(i.get_fattime)
    i.get_ldnumber                           0x08005fd6   Section        0  ff.o(i.get_ldnumber)
    get_ldnumber                             0x08005fd7   Thumb Code    78  ff.o(i.get_ldnumber)
    i.gpio_af_set                            0x08006024   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_toggle                        0x08006082   Section        0  gd32f4xx_gpio.o(i.gpio_bit_toggle)
    i.gpio_bit_write                         0x08006086   Section        0  gd32f4xx_gpio.o(i.gpio_bit_write)
    i.gpio_config                            0x08006090   Section        0  sdio_sdcard.o(i.gpio_config)
    gpio_config                              0x08006091   Thumb Code   106  sdio_sdcard.o(i.gpio_config)
    i.gpio_input_bit_get                     0x08006104   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08006114   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x08006162   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.iic_delay                              0x080061a4   Section        0  myiic.o(i.iic_delay)
    iic_delay                                0x080061a5   Thumb Code    10  myiic.o(i.iic_delay)
    i.iic_init                               0x080061b0   Section        0  myiic.o(i.iic_init)
    i.iic_send_byte                          0x08006204   Section        0  myiic.o(i.iic_send_byte)
    i.iic_start                              0x08006274   Section        0  myiic.o(i.iic_start)
    i.iic_stop                               0x080062c4   Section        0  myiic.o(i.iic_stop)
    i.iic_wait_ack                           0x08006304   Section        0  myiic.o(i.iic_wait_ack)
    i.init_alloc_info                        0x08006348   Section        0  ff.o(i.init_alloc_info)
    init_alloc_info                          0x08006349   Thumb Code    50  ff.o(i.init_alloc_info)
    i.key_init                               0x0800637c   Section        0  key.o(i.key_init)
    i.key_scan                               0x08006420   Section        0  key.o(i.key_scan)
    i.ld_clust                               0x0800655c   Section        0  ff.o(i.ld_clust)
    ld_clust                                 0x0800655d   Thumb Code    38  ff.o(i.ld_clust)
    i.ld_dword                               0x08006582   Section        0  ff.o(i.ld_dword)
    ld_dword                                 0x08006583   Thumb Code    24  ff.o(i.ld_dword)
    i.ld_qword                               0x0800659a   Section        0  ff.o(i.ld_qword)
    ld_qword                                 0x0800659b   Thumb Code    98  ff.o(i.ld_qword)
    i.ld_word                                0x080065fc   Section        0  ff.o(i.ld_word)
    ld_word                                  0x080065fd   Thumb Code    12  ff.o(i.ld_word)
    i.led_init                               0x08006608   Section        0  led.o(i.led_init)
    i.load_obj_xdir                          0x08006728   Section        0  ff.o(i.load_obj_xdir)
    load_obj_xdir                            0x08006729   Thumb Code    64  ff.o(i.load_obj_xdir)
    i.load_xdir                              0x08006768   Section        0  ff.o(i.load_xdir)
    load_xdir                                0x08006769   Thumb Code   270  ff.o(i.load_xdir)
    i.main                                   0x08006878   Section        0  main.o(i.main)
    i.mount_volume                           0x08006cc4   Section        0  ff.o(i.mount_volume)
    mount_volume                             0x08006cc5   Thumb Code  1128  ff.o(i.mount_volume)
    i.move_window                            0x08007138   Section        0  ff.o(i.move_window)
    move_window                              0x08007139   Thumb Code    52  ff.o(i.move_window)
    i.my_mem_free                            0x0800716c   Section        0  malloc.o(i.my_mem_free)
    my_mem_free                              0x0800716d   Thumb Code    96  malloc.o(i.my_mem_free)
    i.my_mem_init                            0x080071d8   Section        0  malloc.o(i.my_mem_init)
    i.my_mem_malloc                          0x08007208   Section        0  malloc.o(i.my_mem_malloc)
    my_mem_malloc                            0x08007209   Thumb Code   156  malloc.o(i.my_mem_malloc)
    i.my_mem_perused                         0x080072b0   Section        0  malloc.o(i.my_mem_perused)
    i.my_mem_set                             0x080072f0   Section        0  malloc.o(i.my_mem_set)
    i.myfree                                 0x08007304   Section        0  malloc.o(i.myfree)
    i.mymalloc                               0x08007328   Section        0  malloc.o(i.mymalloc)
    i.norflash_erase_sector                  0x08007350   Section        0  norflash.o(i.norflash_erase_sector)
    i.norflash_init                          0x08007394   Section        0  norflash.o(i.norflash_init)
    i.norflash_read                          0x0800744c   Section        0  norflash.o(i.norflash_read)
    i.norflash_read_id                       0x0800749c   Section        0  norflash.o(i.norflash_read_id)
    i.norflash_read_sr                       0x080074ec   Section        0  norflash.o(i.norflash_read_sr)
    i.norflash_send_address                  0x08007548   Section        0  norflash.o(i.norflash_send_address)
    i.norflash_wait_busy                     0x08007580   Section        0  norflash.o(i.norflash_wait_busy)
    i.norflash_write                         0x08007594   Section        0  norflash.o(i.norflash_write)
    i.norflash_write_enable                  0x0800764c   Section        0  norflash.o(i.norflash_write_enable)
    i.norflash_write_nocheck                 0x08007678   Section        0  norflash.o(i.norflash_write_nocheck)
    i.norflash_write_page                    0x080076bc   Section        0  norflash.o(i.norflash_write_page)
    i.norflash_write_sr                      0x08007714   Section        0  norflash.o(i.norflash_write_sr)
    i.nvic_irq_enable                        0x0800776c   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x08007830   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.oled_clear                             0x08007844   Section        0  oled.o(i.oled_clear)
    i.oled_display_status                    0x08007874   Section        0  main.o(i.oled_display_status)
    i.oled_draw_point                        0x0800790c   Section        0  oled.o(i.oled_draw_point)
    i.oled_init                              0x0800796c   Section        0  oled.o(i.oled_init)
    i.oled_refresh_gram                      0x08007a40   Section        0  oled.o(i.oled_refresh_gram)
    i.oled_show_char                         0x08007aa8   Section        0  oled.o(i.oled_show_char)
    i.oled_show_string                       0x08007bc8   Section        0  oled.o(i.oled_show_string)
    i.oled_wr_byte                           0x08007c26   Section        0  oled.o(i.oled_wr_byte)
    oled_wr_byte                             0x08007c27   Thumb Code    56  oled.o(i.oled_wr_byte)
    i.pick_lfn                               0x08007c60   Section        0  ff.o(i.pick_lfn)
    pick_lfn                                 0x08007c61   Thumb Code   136  ff.o(i.pick_lfn)
    i.pmu_backup_write_enable                0x08007cec   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.process_adc_sample                     0x08007d00   Section        0  main.o(i.process_adc_sample)
    i.put_fat                                0x08007ee0   Section        0  ff.o(i.put_fat)
    put_fat                                  0x08007ee1   Thumb Code   324  ff.o(i.put_fat)
    i.put_lfn                                0x08008024   Section        0  ff.o(i.put_lfn)
    put_lfn                                  0x08008025   Thumb Code   124  ff.o(i.put_lfn)
    i.r1_error_check                         0x080080a4   Section        0  sdio_sdcard.o(i.r1_error_check)
    r1_error_check                           0x080080a5   Thumb Code   120  sdio_sdcard.o(i.r1_error_check)
    i.r1_error_type_check                    0x08008128   Section        0  sdio_sdcard.o(i.r1_error_type_check)
    r1_error_type_check                      0x08008129   Thumb Code   174  sdio_sdcard.o(i.r1_error_type_check)
    i.r2_error_check                         0x080081d8   Section        0  sdio_sdcard.o(i.r2_error_check)
    r2_error_check                           0x080081d9   Thumb Code    70  sdio_sdcard.o(i.r2_error_check)
    i.r3_error_check                         0x08008228   Section        0  sdio_sdcard.o(i.r3_error_check)
    r3_error_check                           0x08008229   Thumb Code    52  sdio_sdcard.o(i.r3_error_check)
    i.r6_error_check                         0x08008264   Section        0  sdio_sdcard.o(i.r6_error_check)
    r6_error_check                           0x08008265   Thumb Code   158  sdio_sdcard.o(i.r6_error_check)
    i.r7_error_check                         0x0800830c   Section        0  sdio_sdcard.o(i.r7_error_check)
    r7_error_check                           0x0800830d   Thumb Code    82  sdio_sdcard.o(i.r7_error_check)
    i.rcu_clock_freq_get                     0x08008368   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_config                             0x0800848c   Section        0  sdio_sdcard.o(i.rcu_config)
    rcu_config                               0x0800848d   Thumb Code    36  sdio_sdcard.o(i.rcu_config)
    i.rcu_flag_get                           0x080084b0   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x080084d4   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x080084f8   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x08008654   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x08008678   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x0800869c   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_rtc_clock_config                   0x080086c0   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.read_config_file                       0x080086d8   Section        0  main.o(i.read_config_file)
    i.read_config_from_flash                 0x08008818   Section        0  main.o(i.read_config_from_flash)
    i.remove_chain                           0x08008908   Section        0  ff.o(i.remove_chain)
    remove_chain                             0x08008909   Thumb Code   330  ff.o(i.remove_chain)
    i.rtc_bcd2dec                            0x08008a52   Section        0  rtc.o(i.rtc_bcd2dec)
    rtc_bcd2dec                              0x08008a53   Thumb Code    22  rtc.o(i.rtc_bcd2dec)
    i.rtc_config                             0x08008a68   Section        0  rtc.o(i.rtc_config)
    i.rtc_current_time_get                   0x08008b34   Section        0  gd32f4xx_rtc.o(i.rtc_current_time_get)
    i.rtc_dec2bcd                            0x08008b98   Section        0  rtc.o(i.rtc_dec2bcd)
    rtc_dec2bcd                              0x08008b99   Thumb Code    28  rtc.o(i.rtc_dec2bcd)
    i.rtc_flag_clear                         0x08008bb4   Section        0  gd32f4xx_rtc.o(i.rtc_flag_clear)
    i.rtc_flag_get                           0x08008bc4   Section        0  gd32f4xx_rtc.o(i.rtc_flag_get)
    i.rtc_get_date                           0x08008bd8   Section        0  rtc.o(i.rtc_get_date)
    i.rtc_get_time                           0x08008c18   Section        0  rtc.o(i.rtc_get_time)
    i.rtc_init                               0x08008c5c   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x08008d38   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08008d80   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_register_sync_wait                 0x08008d94   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.rtc_set_date                           0x08008df4   Section        0  rtc.o(i.rtc_set_date)
    i.rtc_set_time                           0x08008e44   Section        0  rtc.o(i.rtc_set_time)
    i.save_config_to_flash                   0x08008e94   Section        0  main.o(i.save_config_to_flash)
    i.sd_block_read                          0x08008f48   Section        0  sdio_sdcard.o(i.sd_block_read)
    i.sd_block_write                         0x0800919c   Section        0  sdio_sdcard.o(i.sd_block_write)
    i.sd_bus_mode_config                     0x08009514   Section        0  sdio_sdcard.o(i.sd_bus_mode_config)
    i.sd_bus_width_config                    0x080095a8   Section        0  sdio_sdcard.o(i.sd_bus_width_config)
    sd_bus_width_config                      0x080095a9   Thumb Code   242  sdio_sdcard.o(i.sd_bus_width_config)
    i.sd_card_capacity_get                   0x080096a4   Section        0  sdio_sdcard.o(i.sd_card_capacity_get)
    i.sd_card_information_get                0x0800974c   Section        0  sdio_sdcard.o(i.sd_card_information_get)
    i.sd_card_init                           0x08009a0c   Section        0  sdio_sdcard.o(i.sd_card_init)
    i.sd_card_select_deselect                0x08009b28   Section        0  sdio_sdcard.o(i.sd_card_select_deselect)
    i.sd_card_state_get                      0x08009b50   Section        0  sdio_sdcard.o(i.sd_card_state_get)
    sd_card_state_get                        0x08009b51   Thumb Code   166  sdio_sdcard.o(i.sd_card_state_get)
    i.sd_cardstatus_get                      0x08009c08   Section        0  sdio_sdcard.o(i.sd_cardstatus_get)
    i.sd_datablocksize_get                   0x08009c50   Section        0  sdio_sdcard.o(i.sd_datablocksize_get)
    sd_datablocksize_get                     0x08009c51   Thumb Code    24  sdio_sdcard.o(i.sd_datablocksize_get)
    i.sd_init                                0x08009c68   Section        0  sdio_sdcard.o(i.sd_init)
    i.sd_interrupts_process                  0x08009cb0   Section        0  sdio_sdcard.o(i.sd_interrupts_process)
    i.sd_lock_unlock                         0x08009de0   Section        0  sdio_sdcard.o(i.sd_lock_unlock)
    i.sd_multiblocks_read                    0x08009fd0   Section        0  sdio_sdcard.o(i.sd_multiblocks_read)
    i.sd_multiblocks_write                   0x0800a2a8   Section        0  sdio_sdcard.o(i.sd_multiblocks_write)
    i.sd_power_on                            0x0800a694   Section        0  sdio_sdcard.o(i.sd_power_on)
    i.sd_read_disk                           0x0800a7c0   Section        0  sd_conf.o(i.sd_read_disk)
    i.sd_scr_get                             0x0800a7f4   Section        0  sdio_sdcard.o(i.sd_scr_get)
    sd_scr_get                               0x0800a7f5   Thumb Code   344  sdio_sdcard.o(i.sd_scr_get)
    i.sd_transfer_mode_config                0x0800a950   Section        0  sdio_sdcard.o(i.sd_transfer_mode_config)
    i.sd_transfer_stop                       0x0800a968   Section        0  sdio_sdcard.o(i.sd_transfer_stop)
    i.sd_write_disk                          0x0800a98c   Section        0  sd_conf.o(i.sd_write_disk)
    i.sdio_bus_mode_set                      0x0800a9c0   Section        0  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    i.sdio_clock_config                      0x0800a9dc   Section        0  gd32f4xx_sdio.o(i.sdio_clock_config)
    i.sdio_clock_enable                      0x0800aa10   Section        0  gd32f4xx_sdio.o(i.sdio_clock_enable)
    i.sdio_command_index_get                 0x0800aa24   Section        0  gd32f4xx_sdio.o(i.sdio_command_index_get)
    i.sdio_command_response_config           0x0800aa30   Section        0  gd32f4xx_sdio.o(i.sdio_command_response_config)
    i.sdio_csm_enable                        0x0800aa68   Section        0  gd32f4xx_sdio.o(i.sdio_csm_enable)
    i.sdio_data_config                       0x0800aa7c   Section        0  gd32f4xx_sdio.o(i.sdio_data_config)
    i.sdio_data_read                         0x0800aab8   Section        0  gd32f4xx_sdio.o(i.sdio_data_read)
    i.sdio_data_transfer_config              0x0800aac4   Section        0  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    i.sdio_data_write                        0x0800aae0   Section        0  gd32f4xx_sdio.o(i.sdio_data_write)
    i.sdio_deinit                            0x0800aaec   Section        0  gd32f4xx_sdio.o(i.sdio_deinit)
    i.sdio_dma_disable                       0x0800ab00   Section        0  gd32f4xx_sdio.o(i.sdio_dma_disable)
    i.sdio_dma_enable                        0x0800ab14   Section        0  gd32f4xx_sdio.o(i.sdio_dma_enable)
    i.sdio_dsm_disable                       0x0800ab28   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    i.sdio_dsm_enable                        0x0800ab3c   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    i.sdio_flag_clear                        0x0800ab50   Section        0  gd32f4xx_sdio.o(i.sdio_flag_clear)
    i.sdio_flag_get                          0x0800ab5c   Section        0  gd32f4xx_sdio.o(i.sdio_flag_get)
    i.sdio_hardware_clock_disable            0x0800ab70   Section        0  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    i.sdio_interrupt_disable                 0x0800ab84   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    i.sdio_interrupt_enable                  0x0800ab94   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    i.sdio_interrupt_flag_clear              0x0800aba4   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    i.sdio_interrupt_flag_get                0x0800abb0   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    i.sdio_power_state_get                   0x0800abc4   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_get)
    i.sdio_power_state_set                   0x0800abd0   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_set)
    i.sdio_response_get                      0x0800abdc   Section        0  gd32f4xx_sdio.o(i.sdio_response_get)
    i.sdio_sd_init                           0x0800ac18   Section        0  sd_conf.o(i.sdio_sd_init)
    i.sdio_wait_type_set                     0x0800ace4   Section        0  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    i.set_limit                              0x0800ad00   Section        0  main.o(i.set_limit)
    i.set_ratio                              0x0800ae80   Section        0  main.o(i.set_ratio)
    i.set_rtc_time                           0x0800b000   Section        0  main.o(i.set_rtc_time)
    i.spi1_init                              0x0800b0bc   Section        0  spi.o(i.spi1_init)
    i.spi1_read_write_byte                   0x0800b1a0   Section        0  spi.o(i.spi1_read_write_byte)
    i.spi1_set_speed                         0x0800b1d8   Section        0  spi.o(i.spi1_set_speed)
    i.spi_disable                            0x0800b208   Section        0  gd32f4xx_spi.o(i.spi_disable)
    i.spi_enable                             0x0800b212   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_i2s_data_receive                   0x0800b21c   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x0800b224   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_deinit                         0x0800b228   Section        0  gd32f4xx_spi.o(i.spi_i2s_deinit)
    i.spi_i2s_flag_get                       0x0800b2d4   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x0800b2e4   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.spi_struct_para_init                   0x0800b316   Section        0  gd32f4xx_spi.o(i.spi_struct_para_init)
    i.st_clust                               0x0800b328   Section        0  ff.o(i.st_clust)
    st_clust                                 0x0800b329   Thumb Code    36  ff.o(i.st_clust)
    i.st_dword                               0x0800b34c   Section        0  ff.o(i.st_dword)
    st_dword                                 0x0800b34d   Thumb Code    24  ff.o(i.st_dword)
    i.st_qword                               0x0800b364   Section        0  ff.o(i.st_qword)
    st_qword                                 0x0800b365   Thumb Code    92  ff.o(i.st_qword)
    i.st_word                                0x0800b3c0   Section        0  ff.o(i.st_word)
    st_word                                  0x0800b3c1   Thumb Code    12  ff.o(i.st_word)
    i.start_sampling                         0x0800b3cc   Section        0  main.o(i.start_sampling)
    i.stop_sampling                          0x0800b4d8   Section        0  main.o(i.stop_sampling)
    i.store_hide_data                        0x0800b5bc   Section        0  main.o(i.store_hide_data)
    i.store_log_entry                        0x0800b700   Section        0  main.o(i.store_log_entry)
    i.store_overlimit_data                   0x0800b860   Section        0  main.o(i.store_overlimit_data)
    i.store_sample_data                      0x0800b9c0   Section        0  main.o(i.store_sample_data)
    i.store_xdir                             0x0800bb24   Section        0  ff.o(i.store_xdir)
    store_xdir                               0x0800bb25   Thumb Code   114  ff.o(i.store_xdir)
    i.sum_sfn                                0x0800bb96   Section        0  ff.o(i.sum_sfn)
    sum_sfn                                  0x0800bb97   Thumb Code    32  ff.o(i.sum_sfn)
    i.sync_fs                                0x0800bbb8   Section        0  ff.o(i.sync_fs)
    sync_fs                                  0x0800bbb9   Thumb Code   132  ff.o(i.sync_fs)
    i.sync_window                            0x0800bc44   Section        0  ff.o(i.sync_window)
    sync_window                              0x0800bc45   Thumb Code    74  ff.o(i.sync_window)
    i.system_clock_240m_25m_hxtal            0x0800bc90   Section        0  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    system_clock_240m_25m_hxtal              0x0800bc91   Thumb Code   250  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    i.system_clock_config                    0x0800bd98   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x0800bd99   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.system_self_test                       0x0800bda0   Section        0  main.o(i.system_self_test)
    i.tchar2uni                              0x0800bf00   Section        0  ff.o(i.tchar2uni)
    tchar2uni                                0x0800bf01   Thumb Code    76  ff.o(i.tchar2uni)
    i.timer_deinit                           0x0800bf4c   Section        0  gd32f4xx_timer.o(i.timer_deinit)
    i.timer_disable                          0x0800c0d0   Section        0  gd32f4xx_timer.o(i.timer_disable)
    i.timer_enable                           0x0800c0da   Section        0  gd32f4xx_timer.o(i.timer_enable)
    i.timer_init                             0x0800c0e4   Section        0  gd32f4xx_timer.o(i.timer_init)
    i.timer_interrupt_enable                 0x0800c17c   Section        0  gd32f4xx_timer.o(i.timer_interrupt_enable)
    i.timer_interrupt_flag_clear             0x0800c184   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    i.timer_interrupt_flag_get               0x0800c18a   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    i.timer_struct_para_init                 0x0800c1a2   Section        0  gd32f4xx_timer.o(i.timer_struct_para_init)
    i.timerx_int_init                        0x0800c1b8   Section        0  timer.o(i.timerx_int_init)
    i.update_oled_time                       0x0800c214   Section        0  main.o(i.update_oled_time)
    i.usart_baudrate_set                     0x0800c2d4   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x0800c3bc   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x0800c3c6   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x0800c3d0   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_enable                           0x0800c4ac   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x0800c4b6   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_init                             0x0800c4d4   Section        0  usart.o(i.usart_init)
    i.usart_interrupt_enable                 0x0800c59c   Section        0  gd32f4xx_usart.o(i.usart_interrupt_enable)
    i.usart_interrupt_flag_get               0x0800c5b6   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_parity_config                    0x0800c5ee   Section        0  gd32f4xx_usart.o(i.usart_parity_config)
    i.usart_receive_config                   0x0800c5fe   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_stop_bit_set                     0x0800c60e   Section        0  gd32f4xx_usart.o(i.usart_stop_bit_set)
    i.usart_transmit_config                  0x0800c61e   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.usart_word_length_set                  0x0800c62e   Section        0  gd32f4xx_usart.o(i.usart_word_length_set)
    i.validate                               0x0800c63e   Section        0  ff.o(i.validate)
    validate                                 0x0800c63f   Thumb Code    60  ff.o(i.validate)
    i.xdir_sum                               0x0800c67a   Section        0  ff.o(i.xdir_sum)
    xdir_sum                                 0x0800c67b   Thumb Code    58  ff.o(i.xdir_sum)
    i.xname_sum                              0x0800c6b4   Section        0  ff.o(i.xname_sum)
    xname_sum                                0x0800c6b5   Thumb Code    76  ff.o(i.xname_sum)
    locale$$code                             0x0800c700   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x0800c72c   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x0800c758   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x0800c758   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dcheck1                            0x0800c7bc   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x0800c7bc   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x0800c7cc   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x0800c7cc   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$deqf                               0x0800c7e4   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x0800c7e4   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dleqf                              0x0800c85c   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x0800c85c   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0800c8d4   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800c8d4   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800ca28   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800ca28   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800cac4   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800cac4   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800cad0   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x0800cad0   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$f2d                                0x0800cb3c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800cb3c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800cb92   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800cb92   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800cc1e   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800cc1e   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x0800cc28   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x0800cc28   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$ieeestatus                         0x0800cc32   Section        6  istatus.o(x$fpl$ieeestatus)
    $v0                                      0x0800cc32   Number         0  istatus.o(x$fpl$ieeestatus)
    x$fpl$printf1                            0x0800cc38   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800cc38   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x0800cc3c   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x0800cc3c   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x0800cca0   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x0800cca0   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$scanf1                             0x0800ccfc   Section        4  scanf1.o(x$fpl$scanf1)
    $v0                                      0x0800ccfc   Number         0  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x0800cd00   Section        8  scanf2.o(x$fpl$scanf2)
    $v0                                      0x0800cd00   Number         0  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x0800cd08   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x0800cd08   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x0800cd38   Section     6368  oled.o(.constdata)
    x$fpl$usenofp                            0x0800cd38   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800e618   Section       88  ff.o(.constdata)
    LfnOfs                                   0x0800e618   Data          13  ff.o(.constdata)
    DbcTbl                                   0x0800e625   Data          10  ff.o(.constdata)
    badchr                                   0x0800e62f   Data          18  ff.o(.constdata)
    cst                                      0x0800e642   Data          14  ff.o(.constdata)
    cst32                                    0x0800e650   Data          14  ff.o(.constdata)
    defopt                                   0x0800e660   Data          16  ff.o(.constdata)
    .constdata                               0x0800e670   Section    175030  ffunicode.o(.constdata)
    uni2oem936                               0x0800e670   Data       87172  ffunicode.o(.constdata)
    oem2uni936                               0x08023af4   Data       87172  ffunicode.o(.constdata)
    cvt1                                     0x08038f78   Data         498  ffunicode.o(.constdata)
    cvt2                                     0x0803916a   Data         188  ffunicode.o(.constdata)
    .constdata                               0x08039228   Section       24  malloc.o(.constdata)
    .constdata                               0x08039240   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x08039240   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x08039254   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x08039268   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08039268   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08039279   Section       12  _monlen.o(.constdata)
    .constdata                               0x08039288   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08039288   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080392c4   Data          64  bigflt0.o(.constdata)
    c$$dinf                                  0x0803933c   Section        8  fpconst.o(c$$dinf)
    c$$dmax                                  0x08039344   Section        8  fpconst.o(c$$dmax)
    locale$$data                             0x0803934c   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08039350   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08039358   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08039364   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08039366   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08039367   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08039368   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08039368   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0803936c   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08039374   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08039478   Data           0  lc_ctype_c.o(locale$$data)
    .ARM.__AT_0x10000000                     0x10000000   Section    61440  malloc.o(.ARM.__AT_0x10000000)
    mem2base                                 0x10000000   Data       61440  malloc.o(.ARM.__AT_0x10000000)
    .ARM.__AT_0x1000F000                     0x1000f000   Section     1920  malloc.o(.ARM.__AT_0x1000F000)
    mem2mapbase                              0x1000f000   Data        1920  malloc.o(.ARM.__AT_0x1000F000)
    .data                                    0x20000000   Section       63  main.o(.data)
    counter                                  0x20000034   Data           4  main.o(.data)
    last_tick                                0x20000038   Data           4  main.o(.data)
    overlimit_count                          0x2000003c   Data           1  main.o(.data)
    log_initialized                          0x2000003d   Data           1  main.o(.data)
    hide_count                               0x2000003e   Data           1  main.o(.data)
    .data                                    0x20000040   Section        4  delay.o(.data)
    g_fac_us                                 0x20000040   Data           4  delay.o(.data)
    .data                                    0x20000044   Section        6  usart.o(.data)
    .data                                    0x2000004a   Section        1  key.o(.data)
    key_up                                   0x2000004a   Data           1  key.o(.data)
    .data                                    0x2000004c   Section        2  norflash.o(.data)
    .data                                    0x20000050   Section       36  sdio_sdcard.o(.data)
    cardtype                                 0x20000058   Data           1  sdio_sdcard.o(.data)
    sd_rca                                   0x2000005a   Data           2  sdio_sdcard.o(.data)
    transmode                                0x2000005c   Data           4  sdio_sdcard.o(.data)
    totalnumber_bytes                        0x20000060   Data           4  sdio_sdcard.o(.data)
    stopcondition                            0x20000064   Data           4  sdio_sdcard.o(.data)
    transerror                               0x20000068   Data           1  sdio_sdcard.o(.data)
    transend                                 0x2000006c   Data           4  sdio_sdcard.o(.data)
    number_bytes                             0x20000070   Data           4  sdio_sdcard.o(.data)
    .data                                    0x20000074   Section       10  ff.o(.data)
    FatFs                                    0x20000074   Data           8  ff.o(.data)
    Fsid                                     0x2000007c   Data           2  ff.o(.data)
    .data                                    0x20000080   Section       28  malloc.o(.data)
    .bss                                     0x200000a0   Section     2450  main.o(.bss)
    .bss                                     0x20000a32   Section      200  usart.o(.bss)
    .bss                                     0x20000afa   Section      512  oled.o(.bss)
    g_oled_gram                              0x20000afa   Data         512  oled.o(.bss)
    .bss                                     0x20000cfa   Section     4096  norflash.o(.bss)
    .bss                                     0x20001cfc   Section       20  rtc.o(.bss)
    .bss                                     0x20001d10   Section       72  sd_conf.o(.bss)
    .bss                                     0x20001d58   Section       32  sdio_sdcard.o(.bss)
    sd_csd                                   0x20001d58   Data          16  sdio_sdcard.o(.bss)
    sd_cid                                   0x20001d68   Data          16  sdio_sdcard.o(.bss)
    .bss                                     0x20001d80   Section    105600  malloc.o(.bss)
    mem1base                                 0x20001d80   Data       102400  malloc.o(.bss)
    mem1mapbase                              0x2001ad80   Data        3200  malloc.o(.bss)
    .bss                                     0x2001ba00   Section       96  libspace.o(.bss)
    HEAP                                     0x2001ba60   Section     1024  startup_gd32f450_470.o(HEAP)
    Heap_Mem                                 0x2001ba60   Data        1024  startup_gd32f450_470.o(HEAP)
    STACK                                    0x2001be60   Section     1024  startup_gd32f450_470.o(STACK)
    Stack_Mem                                0x2001be60   Data        1024  startup_gd32f450_470.o(STACK)
    __initial_sp                             0x2001c260   Data           0  startup_gd32f450_470.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001e9   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000205   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x08000221   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x08000221   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000227   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800022d   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_x                                0x08000233   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x08000239   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800023f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000243   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000245   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800024f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x0800024f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x0800025b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800025b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x0800025b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000267   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000269   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800026b   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800026b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800026b   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000271   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000271   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000275   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000275   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800027d   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800027f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800027f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000283   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000289   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER3_IRQHandler                        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x080002a3   Thumb Code     0  startup_gd32f450_470.o(.text)
    __user_initial_stackheap                 0x080002a5   Thumb Code    10  startup_gd32f450_470.o(.text)
    __use_no_semihosting                     0x080002c9   Thumb Code     2  use_no_semi_2.o(.text)
    __aeabi_uldivmod                         0x080002cb   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080002cb   Thumb Code   238  lludivv7m.o(.text)
    __2printf                                0x080003b9   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x080003d1   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x080003f9   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000425   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x08000447   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000499   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x08000511   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x08000511   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x08000569   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __0sscanf                                0x080006f1   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x0800072d   Thumb Code   332  _scanf_int.o(.text)
    strchr                                   0x08000879   Thumb Code    20  strchr.o(.text)
    strstr                                   0x0800088d   Thumb Code    36  strstr.o(.text)
    memcmp                                   0x080008b1   Thumb Code    88  memcmp.o(.text)
    strlen                                   0x08000909   Thumb Code    62  strlen.o(.text)
    strncmp                                  0x08000947   Thumb Code   150  strncmp.o(.text)
    strcat                                   0x080009dd   Thumb Code    24  strcat.o(.text)
    __aeabi_memcpy                           0x080009f5   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080009f5   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000a5b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x08000a7f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000a7f   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000a7f   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000ac7   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memset                           0x08000ae3   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr                           0x08000af3   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000af3   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000af7   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000b37   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000b37   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000b37   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000b3b   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x08000b85   Thumb Code   128  strcmpv7m.o(.text)
    mktime                                   0x08000c43   Thumb Code   356  mktime.o(.text)
    __use_two_region_memory                  0x08000db1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000db3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000db5   Thumb Code     2  heapauxi.o(.text)
    __semihosting$guard                      0x08000db7   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000db7   Thumb Code     2  use_no_semi.o(.text)
    _ll_udiv10                               0x08000db9   Thumb Code   138  lludiv10.o(.text)
    __read_errno                             0x08000e43   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000e4d   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x08000e59   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000f0b   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080010bd   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08001333   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08001359   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08001363   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001377   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001387   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08001391   Thumb Code    32  _printf_char_file.o(.text)
    _chval                                   0x080013b5   Thumb Code    28  _chval.o(.text)
    _scanf_really_real                       0x0800161d   Thumb Code   684  scanf_fp.o(.text)
    __vfscanf_char                           0x080018d5   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x080018f5   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08001913   Thumb Code    34  _sgetc.o(.text)
    __strtod_int                             0x08001971   Thumb Code    90  strtod.o(.text)
    __rt_locale                              0x080019d9   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x080019e1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080019e1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080019e1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    isspace                                  0x080019e9   Thumb Code    18  isspace.o(.text)
    _printf_fp_infnan                        0x080019fd   Thumb Code   112  _printf_fp_infnan.o(.text)
    __vfscanf                                0x08001a7d   Thumb Code   880  _scanf.o(.text)
    _btod_etento                             0x08001df1   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08001ed5   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08001edd   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001edd   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001edd   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001ee5   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001f31   Thumb Code    16  rt_ctype_table.o(.text)
    _scanf_really_hex_real                   0x08001f41   Thumb Code   786  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x08002261   Thumb Code   292  scanf_infnan.o(.text)
    exit                                     0x08002395   Thumb Code    18  exit.o(.text)
    __aeabi_llsl                             0x080023a7   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x080023a7   Thumb Code    38  llshl.o(.text)
    _btod_d2e                                0x080023cd   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x0800240b   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08002451   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080024b1   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x080027e9   Thumb Code   122  btod.o(CL$$btod_e2d)
    _e2e                                     0x0800286d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002949   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x08002973   Thumb Code    42  btod.o(CL$$btod_edivd)
    _btod_emul                               0x0800299d   Thumb Code    42  btod.o(CL$$btod_emul)
    _btod_emuld                              0x080029c7   Thumb Code    42  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x080029f1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08002c35   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08002c39   Thumb Code     4  gd32f4xx_it.o(i.DebugMon_Handler)
    HardFault_Handler                        0x08002c3d   Thumb Code     4  gd32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08002c41   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08002c45   Thumb Code     4  gd32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08002c49   Thumb Code     4  gd32f4xx_it.o(i.PendSV_Handler)
    RTC_Alarm_IRQHandler                     0x08002c4d   Thumb Code    36  rtc.o(i.RTC_Alarm_IRQHandler)
    RTC_WKUP_IRQHandler                      0x08002c7d   Thumb Code    42  rtc.o(i.RTC_WKUP_IRQHandler)
    SDIO_IRQHandler                          0x08002cad   Thumb Code     8  sd_conf.o(i.SDIO_IRQHandler)
    SVC_Handler                              0x08002cb5   Thumb Code     4  gd32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08002cb9   Thumb Code     2  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08002cbd   Thumb Code   194  system_gd32f4xx.o(i.SystemInit)
    TIMER6_IRQHandler                        0x08002d91   Thumb Code    98  main.o(i.TIMER6_IRQHandler)
    USART0_IRQHandler                        0x08002e11   Thumb Code   120  usart.o(i.USART0_IRQHandler)
    UsageFault_Handler                       0x08002e99   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08002e9d   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp___mathlib_tofloat               0x08002ed1   Thumb Code   232  narrow.o(i.__hardfp___mathlib_tofloat)
    __hardfp_atof                            0x08002fc9   Thumb Code    44  atof.o(i.__hardfp_atof)
    __hardfp_ldexp                           0x08003001   Thumb Code   200  ldexp.o(i.__hardfp_ldexp)
    __mathlib_dbl_overflow                   0x080030d1   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x080030f1   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_narrow                         0x08003111   Thumb Code    18  narrow.o(i.__mathlib_narrow)
    __support_ldexp                          0x08003123   Thumb Code    20  ldexp.o(i.__support_ldexp)
    _is_digit                                0x08003137   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x08003145   Thumb Code     4  usart.o(i._sys_exit)
    adc_calibration_enable                   0x08003149   Thumb Code    42  gd32f4xx_adc.o(i.adc_calibration_enable)
    adc_channel_length_config                0x08003173   Thumb Code    82  gd32f4xx_adc.o(i.adc_channel_length_config)
    adc_clock_config                         0x080031c5   Thumb Code    28  gd32f4xx_adc.o(i.adc_clock_config)
    adc_data_alignment_config                0x080031e9   Thumb Code    22  gd32f4xx_adc.o(i.adc_data_alignment_config)
    adc_deinit                               0x080031ff   Thumb Code    20  gd32f4xx_adc.o(i.adc_deinit)
    adc_enable                               0x08003213   Thumb Code    18  gd32f4xx_adc.o(i.adc_enable)
    adc_external_trigger_config              0x08003225   Thumb Code    52  gd32f4xx_adc.o(i.adc_external_trigger_config)
    adc_flag_clear                           0x08003259   Thumb Code     8  gd32f4xx_adc.o(i.adc_flag_clear)
    adc_flag_get                             0x08003261   Thumb Code    14  gd32f4xx_adc.o(i.adc_flag_get)
    adc_get_result                           0x08003271   Thumb Code    54  adc.o(i.adc_get_result)
    adc_get_result_average                   0x080032ad   Thumb Code    44  adc.o(i.adc_get_result_average)
    adc_init                                 0x080032d9   Thumb Code   126  adc.o(i.adc_init)
    adc_resolution_config                    0x08003361   Thumb Code    16  gd32f4xx_adc.o(i.adc_resolution_config)
    adc_routine_channel_config               0x08003371   Thumb Code   172  gd32f4xx_adc.o(i.adc_routine_channel_config)
    adc_routine_data_read                    0x0800341d   Thumb Code     8  gd32f4xx_adc.o(i.adc_routine_data_read)
    adc_software_trigger_enable              0x08003425   Thumb Code    36  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    adc_special_function_config              0x08003449   Thumb Code    90  gd32f4xx_adc.o(i.adc_special_function_config)
    adc_sync_mode_config                     0x080034a5   Thumb Code    28  gd32f4xx_adc.o(i.adc_sync_mode_config)
    adjust_sample_cycle                      0x080034c9   Thumb Code   140  main.o(i.adjust_sample_cycle)
    datetime_to_unix                         0x08003d2d   Thumb Code    50  main.o(i.datetime_to_unix)
    delay_init                               0x08003ddd   Thumb Code    38  delay.o(i.delay_init)
    delay_ms                                 0x08003e09   Thumb Code    18  delay.o(i.delay_ms)
    delay_us                                 0x08003e1d   Thumb Code    68  delay.o(i.delay_us)
    dir_sdi                                  0x08004557   Thumb Code   170  ff.o(i.dir_sdi)
    disk_initialize                          0x08004601   Thumb Code    48  diskio.o(i.disk_initialize)
    disk_ioctl                               0x08004631   Thumb Code   138  diskio.o(i.disk_ioctl)
    disk_read                                0x080046c1   Thumb Code   126  diskio.o(i.disk_read)
    disk_status                              0x0800473f   Thumb Code     6  diskio.o(i.disk_status)
    disk_write                               0x08004745   Thumb Code   126  diskio.o(i.disk_write)
    dma_channel_disable                      0x080047c3   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_disable)
    dma_channel_enable                       0x080047e3   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_enable)
    dma_channel_subperipheral_select         0x08004803   Thumb Code    38  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    dma_deinit                               0x08004829   Thumb Code   166  gd32f4xx_dma.o(i.dma_deinit)
    dma_flag_clear                           0x080048cf   Thumb Code    62  gd32f4xx_dma.o(i.dma_flag_clear)
    dma_flag_get                             0x0800490d   Thumb Code    76  gd32f4xx_dma.o(i.dma_flag_get)
    dma_flow_controller_config               0x08004959   Thumb Code    64  gd32f4xx_dma.o(i.dma_flow_controller_config)
    dma_multi_data_mode_init                 0x08004999   Thumb Code   352  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    encode_voltage                           0x08004c65   Thumb Code    74  main.o(i.encode_voltage)
    exti_flag_clear                          0x08004cb5   Thumb Code     6  gd32f4xx_exti.o(i.exti_flag_clear)
    f_close                                  0x08004cc1   Thumb Code    38  ff.o(i.f_close)
    f_gets                                   0x08004ce7   Thumb Code    80  ff.o(i.f_gets)
    f_mkdir                                  0x08004d37   Thumb Code   418  ff.o(i.f_mkdir)
    f_mount                                  0x08004ed9   Thumb Code    80  ff.o(i.f_mount)
    f_open                                   0x08004f2d   Thumb Code   786  ff.o(i.f_open)
    f_read                                   0x0800523f   Thumb Code   552  ff.o(i.f_read)
    f_sync                                   0x08005467   Thumb Code   424  ff.o(i.f_sync)
    f_write                                  0x0800560f   Thumb Code   644  ff.o(i.f_write)
    ff_memalloc                              0x08005893   Thumb Code    14  ffsystem.o(i.ff_memalloc)
    ff_memfree                               0x080058a1   Thumb Code    14  ffsystem.o(i.ff_memfree)
    ff_oem2uni                               0x080058b1   Thumb Code   100  ffunicode.o(i.ff_oem2uni)
    ff_uni2oem                               0x08005919   Thumb Code   112  ffunicode.o(i.ff_uni2oem)
    ff_wtoupper                              0x0800598d   Thumb Code   166  ffunicode.o(i.ff_wtoupper)
    fputc                                    0x08005c99   Thumb Code    32  usart.o(i.fputc)
    frexp                                    0x08005cc1   Thumb Code   118  frexp.o(i.frexp)
    get_current_time                         0x08005e21   Thumb Code    30  main.o(i.get_current_time)
    get_fattime                              0x08005fd3   Thumb Code     4  ffsystem.o(i.get_fattime)
    gpio_af_set                              0x08006025   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_toggle                          0x08006083   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_toggle)
    gpio_bit_write                           0x08006087   Thumb Code    10  gd32f4xx_gpio.o(i.gpio_bit_write)
    gpio_input_bit_get                       0x08006105   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08006115   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x08006163   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    iic_init                                 0x080061b1   Thumb Code    80  myiic.o(i.iic_init)
    iic_send_byte                            0x08006205   Thumb Code   106  myiic.o(i.iic_send_byte)
    iic_start                                0x08006275   Thumb Code    76  myiic.o(i.iic_start)
    iic_stop                                 0x080062c5   Thumb Code    60  myiic.o(i.iic_stop)
    iic_wait_ack                             0x08006305   Thumb Code    64  myiic.o(i.iic_wait_ack)
    key_init                                 0x0800637d   Thumb Code   152  key.o(i.key_init)
    key_scan                                 0x08006421   Thumb Code   300  key.o(i.key_scan)
    led_init                                 0x08006609   Thumb Code   280  led.o(i.led_init)
    main                                     0x08006879   Thumb Code   684  main.o(i.main)
    my_mem_init                              0x080071d9   Thumb Code    38  malloc.o(i.my_mem_init)
    my_mem_perused                           0x080072b1   Thumb Code    56  malloc.o(i.my_mem_perused)
    my_mem_set                               0x080072f1   Thumb Code    20  malloc.o(i.my_mem_set)
    myfree                                   0x08007305   Thumb Code    30  malloc.o(i.myfree)
    mymalloc                                 0x08007329   Thumb Code    34  malloc.o(i.mymalloc)
    norflash_erase_sector                    0x08007351   Thumb Code    62  norflash.o(i.norflash_erase_sector)
    norflash_init                            0x08007395   Thumb Code   166  norflash.o(i.norflash_init)
    norflash_read                            0x0800744d   Thumb Code    74  norflash.o(i.norflash_read)
    norflash_read_id                         0x0800749d   Thumb Code    76  norflash.o(i.norflash_read_id)
    norflash_read_sr                         0x080074ed   Thumb Code    88  norflash.o(i.norflash_read_sr)
    norflash_send_address                    0x08007549   Thumb Code    52  norflash.o(i.norflash_send_address)
    norflash_wait_busy                       0x08007581   Thumb Code    20  norflash.o(i.norflash_wait_busy)
    norflash_write                           0x08007595   Thumb Code   178  norflash.o(i.norflash_write)
    norflash_write_enable                    0x0800764d   Thumb Code    40  norflash.o(i.norflash_write_enable)
    norflash_write_nocheck                   0x08007679   Thumb Code    68  norflash.o(i.norflash_write_nocheck)
    norflash_write_page                      0x080076bd   Thumb Code    82  norflash.o(i.norflash_write_page)
    norflash_write_sr                        0x08007715   Thumb Code    84  norflash.o(i.norflash_write_sr)
    nvic_irq_enable                          0x0800776d   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x08007831   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    oled_clear                               0x08007845   Thumb Code    42  oled.o(i.oled_clear)
    oled_display_status                      0x08007875   Thumb Code   108  main.o(i.oled_display_status)
    oled_draw_point                          0x0800790d   Thumb Code    92  oled.o(i.oled_draw_point)
    oled_init                                0x0800796d   Thumb Code   212  oled.o(i.oled_init)
    oled_refresh_gram                        0x08007a41   Thumb Code   100  oled.o(i.oled_refresh_gram)
    oled_show_char                           0x08007aa9   Thumb Code   274  oled.o(i.oled_show_char)
    oled_show_string                         0x08007bc9   Thumb Code    94  oled.o(i.oled_show_string)
    pmu_backup_write_enable                  0x08007ced   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    process_adc_sample                       0x08007d01   Thumb Code   356  main.o(i.process_adc_sample)
    rcu_clock_freq_get                       0x08008369   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x080084b1   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x080084d5   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x080084f9   Thumb Code   342  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x08008655   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08008679   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x0800869d   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_rtc_clock_config                     0x080086c1   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    read_config_file                         0x080086d9   Thumb Code   176  main.o(i.read_config_file)
    read_config_from_flash                   0x08008819   Thumb Code   164  main.o(i.read_config_from_flash)
    rtc_config                               0x08008a69   Thumb Code   192  rtc.o(i.rtc_config)
    rtc_current_time_get                     0x08008b35   Thumb Code    96  gd32f4xx_rtc.o(i.rtc_current_time_get)
    rtc_flag_clear                           0x08008bb5   Thumb Code    12  gd32f4xx_rtc.o(i.rtc_flag_clear)
    rtc_flag_get                             0x08008bc5   Thumb Code    16  gd32f4xx_rtc.o(i.rtc_flag_get)
    rtc_get_date                             0x08008bd9   Thumb Code    58  rtc.o(i.rtc_get_date)
    rtc_get_time                             0x08008c19   Thumb Code    62  rtc.o(i.rtc_get_time)
    rtc_init                                 0x08008c5d   Thumb Code   216  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x08008d39   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08008d81   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_register_sync_wait                   0x08008d95   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    rtc_set_date                             0x08008df5   Thumb Code    76  rtc.o(i.rtc_set_date)
    rtc_set_time                             0x08008e45   Thumb Code    76  rtc.o(i.rtc_set_time)
    save_config_to_flash                     0x08008e95   Thumb Code   100  main.o(i.save_config_to_flash)
    sd_block_read                            0x08008f49   Thumb Code   558  sdio_sdcard.o(i.sd_block_read)
    sd_block_write                           0x0800919d   Thumb Code   846  sdio_sdcard.o(i.sd_block_write)
    sd_bus_mode_config                       0x08009515   Thumb Code   144  sdio_sdcard.o(i.sd_bus_mode_config)
    sd_card_capacity_get                     0x080096a5   Thumb Code   160  sdio_sdcard.o(i.sd_card_capacity_get)
    sd_card_information_get                  0x0800974d   Thumb Code   686  sdio_sdcard.o(i.sd_card_information_get)
    sd_card_init                             0x08009a0d   Thumb Code   268  sdio_sdcard.o(i.sd_card_init)
    sd_card_select_deselect                  0x08009b29   Thumb Code    38  sdio_sdcard.o(i.sd_card_select_deselect)
    sd_cardstatus_get                        0x08009c09   Thumb Code    66  sdio_sdcard.o(i.sd_cardstatus_get)
    sd_init                                  0x08009c69   Thumb Code    70  sdio_sdcard.o(i.sd_init)
    sd_interrupts_process                    0x08009cb1   Thumb Code   286  sdio_sdcard.o(i.sd_interrupts_process)
    sd_lock_unlock                           0x08009de1   Thumb Code   470  sdio_sdcard.o(i.sd_lock_unlock)
    sd_multiblocks_read                      0x08009fd1   Thumb Code   692  sdio_sdcard.o(i.sd_multiblocks_read)
    sd_multiblocks_write                     0x0800a2a9   Thumb Code   964  sdio_sdcard.o(i.sd_multiblocks_write)
    sd_power_on                              0x0800a695   Thumb Code   290  sdio_sdcard.o(i.sd_power_on)
    sd_read_disk                             0x0800a7c1   Thumb Code    52  sd_conf.o(i.sd_read_disk)
    sd_transfer_mode_config                  0x0800a951   Thumb Code    20  sdio_sdcard.o(i.sd_transfer_mode_config)
    sd_transfer_stop                         0x0800a969   Thumb Code    36  sdio_sdcard.o(i.sd_transfer_stop)
    sd_write_disk                            0x0800a98d   Thumb Code    52  sd_conf.o(i.sd_write_disk)
    sdio_bus_mode_set                        0x0800a9c1   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    sdio_clock_config                        0x0800a9dd   Thumb Code    44  gd32f4xx_sdio.o(i.sdio_clock_config)
    sdio_clock_enable                        0x0800aa11   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_clock_enable)
    sdio_command_index_get                   0x0800aa25   Thumb Code     8  gd32f4xx_sdio.o(i.sdio_command_index_get)
    sdio_command_response_config             0x0800aa31   Thumb Code    52  gd32f4xx_sdio.o(i.sdio_command_response_config)
    sdio_csm_enable                          0x0800aa69   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_csm_enable)
    sdio_data_config                         0x0800aa7d   Thumb Code    54  gd32f4xx_sdio.o(i.sdio_data_config)
    sdio_data_read                           0x0800aab9   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_read)
    sdio_data_transfer_config                0x0800aac5   Thumb Code    24  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    sdio_data_write                          0x0800aae1   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_write)
    sdio_deinit                              0x0800aaed   Thumb Code    20  gd32f4xx_sdio.o(i.sdio_deinit)
    sdio_dma_disable                         0x0800ab01   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_disable)
    sdio_dma_enable                          0x0800ab15   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_enable)
    sdio_dsm_disable                         0x0800ab29   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    sdio_dsm_enable                          0x0800ab3d   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    sdio_flag_clear                          0x0800ab51   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_flag_clear)
    sdio_flag_get                            0x0800ab5d   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_flag_get)
    sdio_hardware_clock_disable              0x0800ab71   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    sdio_interrupt_disable                   0x0800ab85   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    sdio_interrupt_enable                    0x0800ab95   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    sdio_interrupt_flag_clear                0x0800aba5   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    sdio_interrupt_flag_get                  0x0800abb1   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    sdio_power_state_get                     0x0800abc5   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_get)
    sdio_power_state_set                     0x0800abd1   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_set)
    sdio_response_get                        0x0800abdd   Thumb Code    56  gd32f4xx_sdio.o(i.sdio_response_get)
    sdio_sd_init                             0x0800ac19   Thumb Code   134  sd_conf.o(i.sdio_sd_init)
    sdio_wait_type_set                       0x0800ace5   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    set_limit                                0x0800ad01   Thumb Code   276  main.o(i.set_limit)
    set_ratio                                0x0800ae81   Thumb Code   276  main.o(i.set_ratio)
    set_rtc_time                             0x0800b001   Thumb Code   104  main.o(i.set_rtc_time)
    spi1_init                                0x0800b0bd   Thumb Code   218  spi.o(i.spi1_init)
    spi1_read_write_byte                     0x0800b1a1   Thumb Code    50  spi.o(i.spi1_read_write_byte)
    spi1_set_speed                           0x0800b1d9   Thumb Code    44  spi.o(i.spi1_set_speed)
    spi_disable                              0x0800b209   Thumb Code    10  gd32f4xx_spi.o(i.spi_disable)
    spi_enable                               0x0800b213   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_i2s_data_receive                     0x0800b21d   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x0800b225   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_deinit                           0x0800b229   Thumb Code   162  gd32f4xx_spi.o(i.spi_i2s_deinit)
    spi_i2s_flag_get                         0x0800b2d5   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x0800b2e5   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    spi_struct_para_init                     0x0800b317   Thumb Code    18  gd32f4xx_spi.o(i.spi_struct_para_init)
    start_sampling                           0x0800b3cd   Thumb Code   146  main.o(i.start_sampling)
    stop_sampling                            0x0800b4d9   Thumb Code   130  main.o(i.stop_sampling)
    store_hide_data                          0x0800b5bd   Thumb Code   228  main.o(i.store_hide_data)
    store_log_entry                          0x0800b701   Thumb Code   218  main.o(i.store_log_entry)
    store_overlimit_data                     0x0800b861   Thumb Code   234  main.o(i.store_overlimit_data)
    store_sample_data                        0x0800b9c1   Thumb Code   238  main.o(i.store_sample_data)
    system_self_test                         0x0800bda1   Thumb Code   154  main.o(i.system_self_test)
    timer_deinit                             0x0800bf4d   Thumb Code   374  gd32f4xx_timer.o(i.timer_deinit)
    timer_disable                            0x0800c0d1   Thumb Code    10  gd32f4xx_timer.o(i.timer_disable)
    timer_enable                             0x0800c0db   Thumb Code    10  gd32f4xx_timer.o(i.timer_enable)
    timer_init                               0x0800c0e5   Thumb Code   122  gd32f4xx_timer.o(i.timer_init)
    timer_interrupt_enable                   0x0800c17d   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_enable)
    timer_interrupt_flag_clear               0x0800c185   Thumb Code     6  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    timer_interrupt_flag_get                 0x0800c18b   Thumb Code    24  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    timer_struct_para_init                   0x0800c1a3   Thumb Code    22  gd32f4xx_timer.o(i.timer_struct_para_init)
    timerx_int_init                          0x0800c1b9   Thumb Code    86  timer.o(i.timerx_int_init)
    update_oled_time                         0x0800c215   Thumb Code   154  main.o(i.update_oled_time)
    usart_baudrate_set                       0x0800c2d5   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x0800c3bd   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x0800c3c7   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x0800c3d1   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_enable                             0x0800c4ad   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x0800c4b7   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_init                               0x0800c4d5   Thumb Code   190  usart.o(i.usart_init)
    usart_interrupt_enable                   0x0800c59d   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_enable)
    usart_interrupt_flag_get                 0x0800c5b7   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_parity_config                      0x0800c5ef   Thumb Code    16  gd32f4xx_usart.o(i.usart_parity_config)
    usart_receive_config                     0x0800c5ff   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_stop_bit_set                       0x0800c60f   Thumb Code    16  gd32f4xx_usart.o(i.usart_stop_bit_set)
    usart_transmit_config                    0x0800c61f   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    usart_word_length_set                    0x0800c62f   Thumb Code    16  gd32f4xx_usart.o(i.usart_word_length_set)
    _get_lc_numeric                          0x0800c701   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x0800c72d   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x0800c759   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800c759   Thumb Code    98  d2f.o(x$fpl$d2f)
    __fpl_dcheck_NaN1                        0x0800c7bd   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x0800c7cd   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_cdcmpeq                          0x0800c7e5   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x0800c7e5   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_cdcmple                          0x0800c85d   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x0800c85d   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800c8bf   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0800c8d5   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800c8d5   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800ca29   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800cac5   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800cad1   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800cad1   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_f2d                              0x0800cb3d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800cb3d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800cb93   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800cc1f   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800cc27   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800cc27   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x0800cc29   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __ieee_status                            0x0800cc33   Thumb Code     6  istatus.o(x$fpl$ieeestatus)
    _printf_fp_dec                           0x0800cc39   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x0800cc3d   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x0800cca1   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    _scanf_real                              0x0800ccfd   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x0800cd01   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x0800cd05   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x0800cd09   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x0800cd38   Number         0  usenofp.o(x$fpl$usenofp)
    oled_asc2_1206                           0x0800cd38   Data        1140  oled.o(.constdata)
    oled_asc2_1608                           0x0800d1ac   Data        1520  oled.o(.constdata)
    oled_asc2_2412                           0x0800d79c   Data        3420  oled.o(.constdata)
    OLED_HZK_XMZB                            0x0800e4f8   Data         288  oled.o(.constdata)
    memtblsize                               0x08039228   Data           8  malloc.o(.constdata)
    memblksize                               0x08039230   Data           8  malloc.o(.constdata)
    memsize                                  0x08039238   Data           8  malloc.o(.constdata)
    _monlen                                  0x08039279   Data          12  _monlen.o(.constdata)
    Region$$Table$$Base                      0x0803931c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0803933c   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x0803933c   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x0803933c   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x0803933c   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x0803933c   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x0803933c   Data           0  fpconst.o(c$$dinf)
    __dbl_max                                0x08039344   Data           0  fpconst.o(c$$dmax)
    __ctype                                  0x08039375   Data           0  lc_ctype_c.o(locale$$data)
    sys_config                               0x20000000   Data          16  main.o(.data)
    sampling_enabled                         0x20000010   Data           1  main.o(.data)
    sample_count                             0x20000011   Data           1  main.o(.data)
    encrypt_mode                             0x20000012   Data           1  main.o(.data)
    seconds_counter                          0x20000014   Data           4  main.o(.data)
    led_blink_state                          0x20000018   Data           1  main.o(.data)
    last_sample_time                         0x2000001c   Data           4  main.o(.data)
    hide_output_mode                         0x20000020   Data           1  main.o(.data)
    sample_file_open                         0x20000021   Data           1  main.o(.data)
    overlimit_file_open                      0x20000022   Data           1  main.o(.data)
    log_file_open                            0x20000023   Data           1  main.o(.data)
    hide_file_open                           0x20000024   Data           1  main.o(.data)
    timer6_counter                           0x20000028   Data           4  main.o(.data)
    timer6_sample_flag                       0x2000002c   Data           1  main.o(.data)
    timer6_led_flag                          0x2000002d   Data           1  main.o(.data)
    timer6_time_update_flag                  0x2000002e   Data           1  main.o(.data)
    oled_voltage                             0x20000030   Data           4  main.o(.data)
    __stdout                                 0x20000044   Data           4  usart.o(.data)
    g_usart_rx_sta                           0x20000048   Data           2  usart.o(.data)
    g_norflash_type                          0x2000004c   Data           2  norflash.o(.data)
    sd_scr                                   0x20000050   Data           8  sdio_sdcard.o(.data)
    mallco_dev                               0x20000080   Data          28  malloc.o(.data)
    sample_file                              0x200000a0   Data         600  main.o(.bss)
    overlimit_file                           0x200002f8   Data         600  main.o(.bss)
    log_file                                 0x20000550   Data         600  main.o(.bss)
    hide_file                                0x200007a8   Data         600  main.o(.bss)
    current_filename                         0x20000a00   Data          50  main.o(.bss)
    g_usart_rx_buf                           0x20000a32   Data         200  usart.o(.bss)
    g_norflash_buf                           0x20000cfa   Data        4096  norflash.o(.bss)
    rtc_initpara                             0x20001cfc   Data          20  rtc.o(.bss)
    sd_cardinfo                              0x20001d10   Data          72  sd_conf.o(.bss)
    __libspace_start                         0x2001ba00   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2001ba60   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00039514, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00039478, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         8150  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         8512    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000001a   Code   RO         8514    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000202   0x08000202   0x00000002   PAD
    0x08000204   0x08000204   0x0000001c   Code   RO         8516    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000220   0x08000220   0x00000000   Code   RO         8114    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000220   0x08000220   0x00000006   Code   RO         8113    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000226   0x08000226   0x00000006   Code   RO         8111    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800022c   0x0800022c   0x00000006   Code   RO         8112    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000232   0x08000232   0x00000006   Code   RO         8110    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000238   0x08000238   0x00000006   Code   RO         8109    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800023e   0x0800023e   0x00000004   Code   RO         8191    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000242   0x08000242   0x00000002   Code   RO         8324    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000244   0x08000244   0x00000004   Code   RO         8325    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         8328    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         8331    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         8333    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         8335    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000006   Code   RO         8336    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800024e   0x0800024e   0x00000000   Code   RO         8338    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800024e   0x0800024e   0x0000000c   Code   RO         8339    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x0800025a   0x0800025a   0x00000000   Code   RO         8340    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800025a   0x0800025a   0x00000000   Code   RO         8342    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800025a   0x0800025a   0x0000000a   Code   RO         8343    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8344    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8346    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8348    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8350    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8352    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8354    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8356    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8358    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8362    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8364    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8366    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         8368    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000002   Code   RO         8369    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000002   Code   RO         8459    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000268   0x08000268   0x00000000   Code   RO         8489    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000268   0x08000268   0x00000000   Code   RO         8491    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000268   0x08000268   0x00000000   Code   RO         8493    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000268   0x08000268   0x00000000   Code   RO         8496    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000268   0x08000268   0x00000000   Code   RO         8499    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000268   0x08000268   0x00000000   Code   RO         8501    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000268   0x08000268   0x00000000   Code   RO         8504    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000268   0x08000268   0x00000002   Code   RO         8505    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         8170    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         8214    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         8226    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000270   0x08000270   0x00000000   Code   RO         8216    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000270   0x08000270   0x00000004   Code   RO         8217    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         8219    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000274   0x08000274   0x00000008   Code   RO         8220    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800027c   0x0800027c   0x00000002   Code   RO         8380    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         8418    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800027e   0x0800027e   0x00000004   Code   RO         8419    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000282   0x08000282   0x00000006   Code   RO         8420    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000288   0x08000288   0x00000040   Code   RO            4    .text               startup_gd32f450_470.o
    0x080002c8   0x080002c8   0x00000002   Code   RO         8045    .text               c_w.l(use_no_semi_2.o)
    0x080002ca   0x080002ca   0x000000ee   Code   RO         8047    .text               c_w.l(lludivv7m.o)
    0x080003b8   0x080003b8   0x00000018   Code   RO         8053    .text               c_w.l(noretval__2printf.o)
    0x080003d0   0x080003d0   0x00000028   Code   RO         8055    .text               c_w.l(noretval__2sprintf.o)
    0x080003f8   0x080003f8   0x0000004e   Code   RO         8059    .text               c_w.l(_printf_pad.o)
    0x08000446   0x08000446   0x00000052   Code   RO         8061    .text               c_w.l(_printf_str.o)
    0x08000498   0x08000498   0x00000078   Code   RO         8063    .text               c_w.l(_printf_dec.o)
    0x08000510   0x08000510   0x00000058   Code   RO         8070    .text               c_w.l(_printf_hex_int.o)
    0x08000568   0x08000568   0x00000188   Code   RO         8105    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080006f0   0x080006f0   0x0000003c   Code   RO         8116    .text               c_w.l(__0sscanf.o)
    0x0800072c   0x0800072c   0x0000014c   Code   RO         8118    .text               c_w.l(_scanf_int.o)
    0x08000878   0x08000878   0x00000014   Code   RO         8120    .text               c_w.l(strchr.o)
    0x0800088c   0x0800088c   0x00000024   Code   RO         8122    .text               c_w.l(strstr.o)
    0x080008b0   0x080008b0   0x00000058   Code   RO         8124    .text               c_w.l(memcmp.o)
    0x08000908   0x08000908   0x0000003e   Code   RO         8128    .text               c_w.l(strlen.o)
    0x08000946   0x08000946   0x00000096   Code   RO         8130    .text               c_w.l(strncmp.o)
    0x080009dc   0x080009dc   0x00000018   Code   RO         8132    .text               c_w.l(strcat.o)
    0x080009f4   0x080009f4   0x0000008a   Code   RO         8134    .text               c_w.l(rt_memcpy_v6.o)
    0x08000a7e   0x08000a7e   0x00000064   Code   RO         8136    .text               c_w.l(rt_memcpy_w.o)
    0x08000ae2   0x08000ae2   0x00000010   Code   RO         8138    .text               c_w.l(aeabi_memset.o)
    0x08000af2   0x08000af2   0x00000044   Code   RO         8140    .text               c_w.l(rt_memclr.o)
    0x08000b36   0x08000b36   0x0000004e   Code   RO         8142    .text               c_w.l(rt_memclr_w.o)
    0x08000b84   0x08000b84   0x00000080   Code   RO         8144    .text               c_w.l(strcmpv7m.o)
    0x08000c04   0x08000c04   0x000001ac   Code   RO         8146    .text               c_w.l(mktime.o)
    0x08000db0   0x08000db0   0x00000006   Code   RO         8148    .text               c_w.l(heapauxi.o)
    0x08000db6   0x08000db6   0x00000002   Code   RO         8168    .text               c_w.l(use_no_semi.o)
    0x08000db8   0x08000db8   0x0000008a   Code   RO         8175    .text               c_w.l(lludiv10.o)
    0x08000e42   0x08000e42   0x00000016   Code   RO         8177    .text               c_w.l(_rserrno.o)
    0x08000e58   0x08000e58   0x000000b2   Code   RO         8179    .text               c_w.l(_printf_intcommon.o)
    0x08000f0a   0x08000f0a   0x0000041e   Code   RO         8181    .text               c_w.l(_printf_fp_dec.o)
    0x08001328   0x08001328   0x00000030   Code   RO         8183    .text               c_w.l(_printf_char_common.o)
    0x08001358   0x08001358   0x0000000a   Code   RO         8185    .text               c_w.l(_sputc.o)
    0x08001362   0x08001362   0x0000002c   Code   RO         8187    .text               c_w.l(_printf_char.o)
    0x0800138e   0x0800138e   0x00000002   PAD
    0x08001390   0x08001390   0x00000024   Code   RO         8189    .text               c_w.l(_printf_char_file.o)
    0x080013b4   0x080013b4   0x0000001c   Code   RO         8193    .text               c_w.l(_chval.o)
    0x080013d0   0x080013d0   0x000004f8   Code   RO         8195    .text               c_w.l(scanf_fp.o)
    0x080018c8   0x080018c8   0x0000002c   Code   RO         8197    .text               c_w.l(scanf_char.o)
    0x080018f4   0x080018f4   0x00000040   Code   RO         8199    .text               c_w.l(_sgetc.o)
    0x08001934   0x08001934   0x000000a4   Code   RO         8201    .text               c_w.l(strtod.o)
    0x080019d8   0x080019d8   0x00000008   Code   RO         8233    .text               c_w.l(rt_locale_intlibspace.o)
    0x080019e0   0x080019e0   0x00000008   Code   RO         8238    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080019e8   0x080019e8   0x00000012   Code   RO         8240    .text               c_w.l(isspace.o)
    0x080019fa   0x080019fa   0x00000002   PAD
    0x080019fc   0x080019fc   0x00000080   Code   RO         8242    .text               c_w.l(_printf_fp_infnan.o)
    0x08001a7c   0x08001a7c   0x00000374   Code   RO         8246    .text               c_w.l(_scanf.o)
    0x08001df0   0x08001df0   0x000000e4   Code   RO         8248    .text               c_w.l(bigflt0.o)
    0x08001ed4   0x08001ed4   0x00000008   Code   RO         8273    .text               c_w.l(ferror.o)
    0x08001edc   0x08001edc   0x00000008   Code   RO         8302    .text               c_w.l(libspace.o)
    0x08001ee4   0x08001ee4   0x0000004a   Code   RO         8305    .text               c_w.l(sys_stackheap_outer.o)
    0x08001f2e   0x08001f2e   0x00000002   PAD
    0x08001f30   0x08001f30   0x00000010   Code   RO         8307    .text               c_w.l(rt_ctype_table.o)
    0x08001f40   0x08001f40   0x00000320   Code   RO         8309    .text               c_w.l(scanf_hexfp.o)
    0x08002260   0x08002260   0x00000134   Code   RO         8311    .text               c_w.l(scanf_infnan.o)
    0x08002394   0x08002394   0x00000012   Code   RO         8313    .text               c_w.l(exit.o)
    0x080023a6   0x080023a6   0x00000026   Code   RO         8384    .text               c_w.l(llshl.o)
    0x080023cc   0x080023cc   0x0000003e   Code   RO         8251    CL$$btod_d2e        c_w.l(btod.o)
    0x0800240a   0x0800240a   0x00000046   Code   RO         8253    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08002450   0x08002450   0x00000060   Code   RO         8252    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080024b0   0x080024b0   0x00000338   Code   RO         8261    CL$$btod_div_common  c_w.l(btod.o)
    0x080027e8   0x080027e8   0x00000084   Code   RO         8259    CL$$btod_e2d        c_w.l(btod.o)
    0x0800286c   0x0800286c   0x000000dc   Code   RO         8258    CL$$btod_e2e        c_w.l(btod.o)
    0x08002948   0x08002948   0x0000002a   Code   RO         8255    CL$$btod_ediv       c_w.l(btod.o)
    0x08002972   0x08002972   0x0000002a   Code   RO         8257    CL$$btod_edivd      c_w.l(btod.o)
    0x0800299c   0x0800299c   0x0000002a   Code   RO         8254    CL$$btod_emul       c_w.l(btod.o)
    0x080029c6   0x080029c6   0x0000002a   Code   RO         8256    CL$$btod_emuld      c_w.l(btod.o)
    0x080029f0   0x080029f0   0x00000244   Code   RO         8260    CL$$btod_mult_common  c_w.l(btod.o)
    0x08002c34   0x08002c34   0x00000004   Code   RO          353    i.BusFault_Handler  gd32f4xx_it.o
    0x08002c38   0x08002c38   0x00000004   Code   RO          354    i.DebugMon_Handler  gd32f4xx_it.o
    0x08002c3c   0x08002c3c   0x00000004   Code   RO          355    i.HardFault_Handler  gd32f4xx_it.o
    0x08002c40   0x08002c40   0x00000004   Code   RO          356    i.MemManage_Handler  gd32f4xx_it.o
    0x08002c44   0x08002c44   0x00000004   Code   RO          357    i.NMI_Handler       gd32f4xx_it.o
    0x08002c48   0x08002c48   0x00000004   Code   RO          358    i.PendSV_Handler    gd32f4xx_it.o
    0x08002c4c   0x08002c4c   0x00000030   Code   RO         6675    i.RTC_Alarm_IRQHandler  rtc.o
    0x08002c7c   0x08002c7c   0x00000030   Code   RO         6676    i.RTC_WKUP_IRQHandler  rtc.o
    0x08002cac   0x08002cac   0x00000008   Code   RO         6767    i.SDIO_IRQHandler   sd_conf.o
    0x08002cb4   0x08002cb4   0x00000004   Code   RO          359    i.SVC_Handler       gd32f4xx_it.o
    0x08002cb8   0x08002cb8   0x00000002   Code   RO          360    i.SysTick_Handler   gd32f4xx_it.o
    0x08002cba   0x08002cba   0x00000002   PAD
    0x08002cbc   0x08002cbc   0x000000d4   Code   RO          429    i.SystemInit        system_gd32f4xx.o
    0x08002d90   0x08002d90   0x00000080   Code   RO           13    i.TIMER6_IRQHandler  main.o
    0x08002e10   0x08002e10   0x00000088   Code   RO          573    i.USART0_IRQHandler  usart.o
    0x08002e98   0x08002e98   0x00000004   Code   RO          361    i.UsageFault_Handler  gd32f4xx_it.o
    0x08002e9c   0x08002e9c   0x00000030   Code   RO         8292    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08002ecc   0x08002ecc   0x00000004   PAD
    0x08002ed0   0x08002ed0   0x000000f8   Code   RO         8294    i.__hardfp___mathlib_tofloat  m_wm.l(narrow.o)
    0x08002fc8   0x08002fc8   0x00000038   Code   RO         8162    i.__hardfp_atof     m_wm.l(atof.o)
    0x08003000   0x08003000   0x000000d0   Code   RO         8401    i.__hardfp_ldexp    m_wm.l(ldexp.o)
    0x080030d0   0x080030d0   0x00000020   Code   RO         8438    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x080030f0   0x080030f0   0x00000020   Code   RO         8440    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08003110   0x08003110   0x00000012   Code   RO         8295    i.__mathlib_narrow  m_wm.l(narrow.o)
    0x08003122   0x08003122   0x00000014   Code   RO         8403    i.__support_ldexp   m_wm.l(ldexp.o)
    0x08003136   0x08003136   0x0000000e   Code   RO         8098    i._is_digit         c_w.l(__printf_wp.o)
    0x08003144   0x08003144   0x00000004   Code   RO          575    i._sys_exit         usart.o
    0x08003148   0x08003148   0x0000002a   Code   RO          638    i.adc_calibration_enable  gd32f4xx_adc.o
    0x08003172   0x08003172   0x00000052   Code   RO          640    i.adc_channel_length_config  gd32f4xx_adc.o
    0x080031c4   0x080031c4   0x00000024   Code   RO          641    i.adc_clock_config  gd32f4xx_adc.o
    0x080031e8   0x080031e8   0x00000016   Code   RO          642    i.adc_data_alignment_config  gd32f4xx_adc.o
    0x080031fe   0x080031fe   0x00000014   Code   RO          643    i.adc_deinit        gd32f4xx_adc.o
    0x08003212   0x08003212   0x00000012   Code   RO          650    i.adc_enable        gd32f4xx_adc.o
    0x08003224   0x08003224   0x00000034   Code   RO          652    i.adc_external_trigger_config  gd32f4xx_adc.o
    0x08003258   0x08003258   0x00000008   Code   RO          654    i.adc_flag_clear    gd32f4xx_adc.o
    0x08003260   0x08003260   0x0000000e   Code   RO          655    i.adc_flag_get      gd32f4xx_adc.o
    0x0800326e   0x0800326e   0x00000002   PAD
    0x08003270   0x08003270   0x0000003c   Code   RO         6464    i.adc_get_result    adc.o
    0x080032ac   0x080032ac   0x0000002c   Code   RO         6465    i.adc_get_result_average  adc.o
    0x080032d8   0x080032d8   0x00000088   Code   RO         6466    i.adc_init          adc.o
    0x08003360   0x08003360   0x00000010   Code   RO          667    i.adc_resolution_config  gd32f4xx_adc.o
    0x08003370   0x08003370   0x000000ac   Code   RO          668    i.adc_routine_channel_config  gd32f4xx_adc.o
    0x0800341c   0x0800341c   0x00000008   Code   RO          669    i.adc_routine_data_read  gd32f4xx_adc.o
    0x08003424   0x08003424   0x00000024   Code   RO          671    i.adc_software_trigger_enable  gd32f4xx_adc.o
    0x08003448   0x08003448   0x0000005a   Code   RO          672    i.adc_special_function_config  gd32f4xx_adc.o
    0x080034a2   0x080034a2   0x00000002   PAD
    0x080034a4   0x080034a4   0x00000024   Code   RO          677    i.adc_sync_mode_config  gd32f4xx_adc.o
    0x080034c8   0x080034c8   0x000000cc   Code   RO           14    i.adjust_sample_cycle  main.o
    0x08003594   0x08003594   0x0000008a   Code   RO         7190    i.change_bitmap     ff.o
    0x0800361e   0x0800361e   0x00000002   PAD
    0x08003620   0x08003620   0x00000104   Code   RO         7191    i.check_fs          ff.o
    0x08003724   0x08003724   0x00000062   Code   RO         7192    i.clmt_clust        ff.o
    0x08003786   0x08003786   0x0000001a   Code   RO         7193    i.clst2sect         ff.o
    0x080037a0   0x080037a0   0x0000003c   Code   RO         6833    i.cmdsent_error_check  sdio_sdcard.o
    0x080037dc   0x080037dc   0x00000098   Code   RO         7194    i.cmp_lfn           ff.o
    0x08003874   0x08003874   0x000001cc   Code   RO         7195    i.create_chain      ff.o
    0x08003a40   0x08003a40   0x0000025c   Code   RO         7196    i.create_name       ff.o
    0x08003c9c   0x08003c9c   0x00000090   Code   RO         7198    i.create_xdir       ff.o
    0x08003d2c   0x08003d2c   0x00000032   Code   RO           15    i.datetime_to_unix  main.o
    0x08003d5e   0x08003d5e   0x00000002   PAD
    0x08003d60   0x08003d60   0x00000034   Code   RO         7199    i.dbc_1st           ff.o
    0x08003d94   0x08003d94   0x00000048   Code   RO         7200    i.dbc_2nd           ff.o
    0x08003ddc   0x08003ddc   0x0000002c   Code   RO          474    i.delay_init        delay.o
    0x08003e08   0x08003e08   0x00000012   Code   RO          475    i.delay_ms          delay.o
    0x08003e1a   0x08003e1a   0x00000002   PAD
    0x08003e1c   0x08003e1c   0x00000048   Code   RO          476    i.delay_us          delay.o
    0x08003e64   0x08003e64   0x0000007c   Code   RO         7201    i.dir_alloc         ff.o
    0x08003ee0   0x08003ee0   0x000000c2   Code   RO         7202    i.dir_clear         ff.o
    0x08003fa2   0x08003fa2   0x000001a8   Code   RO         7203    i.dir_find          ff.o
    0x0800414a   0x0800414a   0x000000e0   Code   RO         7204    i.dir_next          ff.o
    0x0800422a   0x0800422a   0x00000102   Code   RO         7205    i.dir_read          ff.o
    0x0800432c   0x0800432c   0x0000022a   Code   RO         7206    i.dir_register      ff.o
    0x08004556   0x08004556   0x000000aa   Code   RO         7208    i.dir_sdi           ff.o
    0x08004600   0x08004600   0x00000030   Code   RO         7138    i.disk_initialize   diskio.o
    0x08004630   0x08004630   0x00000090   Code   RO         7139    i.disk_ioctl        diskio.o
    0x080046c0   0x080046c0   0x0000007e   Code   RO         7140    i.disk_read         diskio.o
    0x0800473e   0x0800473e   0x00000006   Code   RO         7141    i.disk_status       diskio.o
    0x08004744   0x08004744   0x0000007e   Code   RO         7142    i.disk_write        diskio.o
    0x080047c2   0x080047c2   0x00000020   Code   RO         1706    i.dma_channel_disable  gd32f4xx_dma.o
    0x080047e2   0x080047e2   0x00000020   Code   RO         1707    i.dma_channel_enable  gd32f4xx_dma.o
    0x08004802   0x08004802   0x00000026   Code   RO         1708    i.dma_channel_subperipheral_select  gd32f4xx_dma.o
    0x08004828   0x08004828   0x000000a6   Code   RO         1711    i.dma_deinit        gd32f4xx_dma.o
    0x080048ce   0x080048ce   0x0000003e   Code   RO         1713    i.dma_flag_clear    gd32f4xx_dma.o
    0x0800490c   0x0800490c   0x0000004c   Code   RO         1714    i.dma_flag_get      gd32f4xx_dma.o
    0x08004958   0x08004958   0x00000040   Code   RO         1715    i.dma_flow_controller_config  gd32f4xx_dma.o
    0x08004998   0x08004998   0x00000164   Code   RO         1724    i.dma_multi_data_mode_init  gd32f4xx_dma.o
    0x08004afc   0x08004afc   0x000000b4   Code   RO         6834    i.dma_receive_config  sdio_sdcard.o
    0x08004bb0   0x08004bb0   0x000000b4   Code   RO         6835    i.dma_transfer_config  sdio_sdcard.o
    0x08004c64   0x08004c64   0x00000050   Code   RO           16    i.encode_voltage    main.o
    0x08004cb4   0x08004cb4   0x0000000c   Code   RO         2744    i.exti_flag_clear   gd32f4xx_exti.o
    0x08004cc0   0x08004cc0   0x00000026   Code   RO         7209    i.f_close           ff.o
    0x08004ce6   0x08004ce6   0x00000050   Code   RO         7213    i.f_gets            ff.o
    0x08004d36   0x08004d36   0x000001a2   Code   RO         7215    i.f_mkdir           ff.o
    0x08004ed8   0x08004ed8   0x00000054   Code   RO         7217    i.f_mount           ff.o
    0x08004f2c   0x08004f2c   0x00000312   Code   RO         7218    i.f_open            ff.o
    0x0800523e   0x0800523e   0x00000228   Code   RO         7223    i.f_read            ff.o
    0x08005466   0x08005466   0x000001a8   Code   RO         7228    i.f_sync            ff.o
    0x0800560e   0x0800560e   0x00000284   Code   RO         7231    i.f_write           ff.o
    0x08005892   0x08005892   0x0000000e   Code   RO         7646    i.ff_memalloc       ffsystem.o
    0x080058a0   0x080058a0   0x0000000e   Code   RO         7647    i.ff_memfree        ffsystem.o
    0x080058ae   0x080058ae   0x00000002   PAD
    0x080058b0   0x080058b0   0x00000068   Code   RO         7679    i.ff_oem2uni        ffunicode.o
    0x08005918   0x08005918   0x00000074   Code   RO         7680    i.ff_uni2oem        ffunicode.o
    0x0800598c   0x0800598c   0x000000b0   Code   RO         7681    i.ff_wtoupper       ffunicode.o
    0x08005a3c   0x08005a3c   0x00000036   Code   RO         7232    i.fill_first_frag   ff.o
    0x08005a72   0x08005a72   0x00000044   Code   RO         7233    i.fill_last_frag    ff.o
    0x08005ab6   0x08005ab6   0x000000a4   Code   RO         7234    i.find_bitmap       ff.o
    0x08005b5a   0x08005b5a   0x00000078   Code   RO         7235    i.find_volume       ff.o
    0x08005bd2   0x08005bd2   0x000000c4   Code   RO         7236    i.follow_path       ff.o
    0x08005c96   0x08005c96   0x00000002   PAD
    0x08005c98   0x08005c98   0x00000024   Code   RO          577    i.fputc             usart.o
    0x08005cbc   0x08005cbc   0x00000004   PAD
    0x08005cc0   0x08005cc0   0x0000008c   Code   RO         8376    i.frexp             m_wm.l(frexp.o)
    0x08005d4c   0x08005d4c   0x000000d4   Code   RO         7237    i.gen_numname       ff.o
    0x08005e20   0x08005e20   0x0000001e   Code   RO           17    i.get_current_time  main.o
    0x08005e3e   0x08005e3e   0x00000194   Code   RO         7238    i.get_fat           ff.o
    0x08005fd2   0x08005fd2   0x00000004   Code   RO         7648    i.get_fattime       ffsystem.o
    0x08005fd6   0x08005fd6   0x0000004e   Code   RO         7240    i.get_ldnumber      ff.o
    0x08006024   0x08006024   0x0000005e   Code   RO         3149    i.gpio_af_set       gd32f4xx_gpio.o
    0x08006082   0x08006082   0x00000004   Code   RO         3152    i.gpio_bit_toggle   gd32f4xx_gpio.o
    0x08006086   0x08006086   0x0000000a   Code   RO         3153    i.gpio_bit_write    gd32f4xx_gpio.o
    0x08006090   0x08006090   0x00000074   Code   RO         6836    i.gpio_config       sdio_sdcard.o
    0x08006104   0x08006104   0x00000010   Code   RO         3155    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08006114   0x08006114   0x0000004e   Code   RO         3157    i.gpio_mode_set     gd32f4xx_gpio.o
    0x08006162   0x08006162   0x00000042   Code   RO         3159    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x080061a4   0x080061a4   0x0000000a   Code   RO         6259    i.iic_delay         myiic.o
    0x080061ae   0x080061ae   0x00000002   PAD
    0x080061b0   0x080061b0   0x00000054   Code   RO         6260    i.iic_init          myiic.o
    0x08006204   0x08006204   0x00000070   Code   RO         6263    i.iic_send_byte     myiic.o
    0x08006274   0x08006274   0x00000050   Code   RO         6264    i.iic_start         myiic.o
    0x080062c4   0x080062c4   0x00000040   Code   RO         6265    i.iic_stop          myiic.o
    0x08006304   0x08006304   0x00000044   Code   RO         6266    i.iic_wait_ack      myiic.o
    0x08006348   0x08006348   0x00000032   Code   RO         7241    i.init_alloc_info   ff.o
    0x0800637a   0x0800637a   0x00000002   PAD
    0x0800637c   0x0800637c   0x000000a4   Code   RO         6433    i.key_init          key.o
    0x08006420   0x08006420   0x0000013c   Code   RO         6434    i.key_scan          key.o
    0x0800655c   0x0800655c   0x00000026   Code   RO         7242    i.ld_clust          ff.o
    0x08006582   0x08006582   0x00000018   Code   RO         7243    i.ld_dword          ff.o
    0x0800659a   0x0800659a   0x00000062   Code   RO         7244    i.ld_qword          ff.o
    0x080065fc   0x080065fc   0x0000000c   Code   RO         7245    i.ld_word           ff.o
    0x08006608   0x08006608   0x00000120   Code   RO         6234    i.led_init          led.o
    0x08006728   0x08006728   0x00000040   Code   RO         7246    i.load_obj_xdir     ff.o
    0x08006768   0x08006768   0x0000010e   Code   RO         7247    i.load_xdir         ff.o
    0x08006876   0x08006876   0x00000002   PAD
    0x08006878   0x08006878   0x0000044c   Code   RO           19    i.main              main.o
    0x08006cc4   0x08006cc4   0x00000474   Code   RO         7248    i.mount_volume      ff.o
    0x08007138   0x08007138   0x00000034   Code   RO         7249    i.move_window       ff.o
    0x0800716c   0x0800716c   0x0000006c   Code   RO         7967    i.my_mem_free       malloc.o
    0x080071d8   0x080071d8   0x00000030   Code   RO         7968    i.my_mem_init       malloc.o
    0x08007208   0x08007208   0x000000a8   Code   RO         7969    i.my_mem_malloc     malloc.o
    0x080072b0   0x080072b0   0x00000040   Code   RO         7970    i.my_mem_perused    malloc.o
    0x080072f0   0x080072f0   0x00000014   Code   RO         7971    i.my_mem_set        malloc.o
    0x08007304   0x08007304   0x00000024   Code   RO         7972    i.myfree            malloc.o
    0x08007328   0x08007328   0x00000028   Code   RO         7973    i.mymalloc          malloc.o
    0x08007350   0x08007350   0x00000044   Code   RO         6564    i.norflash_erase_sector  norflash.o
    0x08007394   0x08007394   0x000000b8   Code   RO         6565    i.norflash_init     norflash.o
    0x0800744c   0x0800744c   0x00000050   Code   RO         6566    i.norflash_read     norflash.o
    0x0800749c   0x0800749c   0x00000050   Code   RO         6567    i.norflash_read_id  norflash.o
    0x080074ec   0x080074ec   0x0000005c   Code   RO         6568    i.norflash_read_sr  norflash.o
    0x08007548   0x08007548   0x00000038   Code   RO         6569    i.norflash_send_address  norflash.o
    0x08007580   0x08007580   0x00000014   Code   RO         6570    i.norflash_wait_busy  norflash.o
    0x08007594   0x08007594   0x000000b8   Code   RO         6571    i.norflash_write    norflash.o
    0x0800764c   0x0800764c   0x0000002c   Code   RO         6573    i.norflash_write_enable  norflash.o
    0x08007678   0x08007678   0x00000044   Code   RO         6574    i.norflash_write_nocheck  norflash.o
    0x080076bc   0x080076bc   0x00000058   Code   RO         6575    i.norflash_write_page  norflash.o
    0x08007714   0x08007714   0x00000058   Code   RO         6576    i.norflash_write_sr  norflash.o
    0x0800776c   0x0800776c   0x000000c4   Code   RO         3738    i.nvic_irq_enable   gd32f4xx_misc.o
    0x08007830   0x08007830   0x00000014   Code   RO         3739    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x08007844   0x08007844   0x00000030   Code   RO         6333    i.oled_clear        oled.o
    0x08007874   0x08007874   0x00000098   Code   RO           20    i.oled_display_status  main.o
    0x0800790c   0x0800790c   0x00000060   Code   RO         6336    i.oled_draw_point   oled.o
    0x0800796c   0x0800796c   0x000000d4   Code   RO         6338    i.oled_init         oled.o
    0x08007a40   0x08007a40   0x00000068   Code   RO         6340    i.oled_refresh_gram  oled.o
    0x08007aa8   0x08007aa8   0x00000120   Code   RO         6341    i.oled_show_char    oled.o
    0x08007bc8   0x08007bc8   0x0000005e   Code   RO         6343    i.oled_show_string  oled.o
    0x08007c26   0x08007c26   0x00000038   Code   RO         6344    i.oled_wr_byte      oled.o
    0x08007c5e   0x08007c5e   0x00000002   PAD
    0x08007c60   0x08007c60   0x0000008c   Code   RO         7250    i.pick_lfn          ff.o
    0x08007cec   0x08007cec   0x00000014   Code   RO         3799    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x08007d00   0x08007d00   0x000001e0   Code   RO           21    i.process_adc_sample  main.o
    0x08007ee0   0x08007ee0   0x00000144   Code   RO         7251    i.put_fat           ff.o
    0x08008024   0x08008024   0x00000080   Code   RO         7252    i.put_lfn           ff.o
    0x080080a4   0x080080a4   0x00000084   Code   RO         6837    i.r1_error_check    sdio_sdcard.o
    0x08008128   0x08008128   0x000000ae   Code   RO         6838    i.r1_error_type_check  sdio_sdcard.o
    0x080081d6   0x080081d6   0x00000002   PAD
    0x080081d8   0x080081d8   0x00000050   Code   RO         6839    i.r2_error_check    sdio_sdcard.o
    0x08008228   0x08008228   0x0000003c   Code   RO         6840    i.r3_error_check    sdio_sdcard.o
    0x08008264   0x08008264   0x000000a8   Code   RO         6841    i.r6_error_check    sdio_sdcard.o
    0x0800830c   0x0800830c   0x0000005c   Code   RO         6842    i.r7_error_check    sdio_sdcard.o
    0x08008368   0x08008368   0x00000124   Code   RO         3951    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x0800848c   0x0800848c   0x00000024   Code   RO         6843    i.rcu_config        sdio_sdcard.o
    0x080084b0   0x080084b0   0x00000024   Code   RO         3954    i.rcu_flag_get      gd32f4xx_rcu.o
    0x080084d4   0x080084d4   0x00000024   Code   RO         3967    i.rcu_osci_on       gd32f4xx_rcu.o
    0x080084f8   0x080084f8   0x0000015c   Code   RO         3968    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x08008654   0x08008654   0x00000024   Code   RO         3970    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x08008678   0x08008678   0x00000024   Code   RO         3973    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x0800869c   0x0800869c   0x00000024   Code   RO         3974    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x080086c0   0x080086c0   0x00000018   Code   RO         3979    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x080086d8   0x080086d8   0x00000140   Code   RO           22    i.read_config_file  main.o
    0x08008818   0x08008818   0x000000f0   Code   RO           23    i.read_config_from_flash  main.o
    0x08008908   0x08008908   0x0000014a   Code   RO         7257    i.remove_chain      ff.o
    0x08008a52   0x08008a52   0x00000016   Code   RO         6677    i.rtc_bcd2dec       rtc.o
    0x08008a68   0x08008a68   0x000000cc   Code   RO         6678    i.rtc_config        rtc.o
    0x08008b34   0x08008b34   0x00000064   Code   RO         4255    i.rtc_current_time_get  gd32f4xx_rtc.o
    0x08008b98   0x08008b98   0x0000001c   Code   RO         6679    i.rtc_dec2bcd       rtc.o
    0x08008bb4   0x08008bb4   0x00000010   Code   RO         4257    i.rtc_flag_clear    gd32f4xx_rtc.o
    0x08008bc4   0x08008bc4   0x00000014   Code   RO         4258    i.rtc_flag_get      gd32f4xx_rtc.o
    0x08008bd8   0x08008bd8   0x00000040   Code   RO         6680    i.rtc_get_date      rtc.o
    0x08008c18   0x08008c18   0x00000044   Code   RO         6681    i.rtc_get_time      rtc.o
    0x08008c5c   0x08008c5c   0x000000dc   Code   RO         4260    i.rtc_init          gd32f4xx_rtc.o
    0x08008d38   0x08008d38   0x00000048   Code   RO         4261    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08008d80   0x08008d80   0x00000014   Code   RO         4262    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08008d94   0x08008d94   0x00000060   Code   RO         4267    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08008df4   0x08008df4   0x00000050   Code   RO         6684    i.rtc_set_date      rtc.o
    0x08008e44   0x08008e44   0x00000050   Code   RO         6685    i.rtc_set_time      rtc.o
    0x08008e94   0x08008e94   0x000000b4   Code   RO           24    i.save_config_to_flash  main.o
    0x08008f48   0x08008f48   0x00000254   Code   RO         6844    i.sd_block_read     sdio_sdcard.o
    0x0800919c   0x0800919c   0x00000378   Code   RO         6845    i.sd_block_write    sdio_sdcard.o
    0x08009514   0x08009514   0x00000094   Code   RO         6846    i.sd_bus_mode_config  sdio_sdcard.o
    0x080095a8   0x080095a8   0x000000fc   Code   RO         6847    i.sd_bus_width_config  sdio_sdcard.o
    0x080096a4   0x080096a4   0x000000a8   Code   RO         6848    i.sd_card_capacity_get  sdio_sdcard.o
    0x0800974c   0x0800974c   0x000002c0   Code   RO         6849    i.sd_card_information_get  sdio_sdcard.o
    0x08009a0c   0x08009a0c   0x0000011c   Code   RO         6850    i.sd_card_init      sdio_sdcard.o
    0x08009b28   0x08009b28   0x00000026   Code   RO         6851    i.sd_card_select_deselect  sdio_sdcard.o
    0x08009b4e   0x08009b4e   0x00000002   PAD
    0x08009b50   0x08009b50   0x000000b8   Code   RO         6852    i.sd_card_state_get  sdio_sdcard.o
    0x08009c08   0x08009c08   0x00000048   Code   RO         6853    i.sd_cardstatus_get  sdio_sdcard.o
    0x08009c50   0x08009c50   0x00000018   Code   RO         6854    i.sd_datablocksize_get  sdio_sdcard.o
    0x08009c68   0x08009c68   0x00000046   Code   RO         6856    i.sd_init           sdio_sdcard.o
    0x08009cae   0x08009cae   0x00000002   PAD
    0x08009cb0   0x08009cb0   0x00000130   Code   RO         6857    i.sd_interrupts_process  sdio_sdcard.o
    0x08009de0   0x08009de0   0x000001f0   Code   RO         6858    i.sd_lock_unlock    sdio_sdcard.o
    0x08009fd0   0x08009fd0   0x000002d8   Code   RO         6859    i.sd_multiblocks_read  sdio_sdcard.o
    0x0800a2a8   0x0800a2a8   0x000003ec   Code   RO         6860    i.sd_multiblocks_write  sdio_sdcard.o
    0x0800a694   0x0800a694   0x0000012c   Code   RO         6862    i.sd_power_on       sdio_sdcard.o
    0x0800a7c0   0x0800a7c0   0x00000034   Code   RO         6769    i.sd_read_disk      sd_conf.o
    0x0800a7f4   0x0800a7f4   0x0000015c   Code   RO         6863    i.sd_scr_get        sdio_sdcard.o
    0x0800a950   0x0800a950   0x00000018   Code   RO         6865    i.sd_transfer_mode_config  sdio_sdcard.o
    0x0800a968   0x0800a968   0x00000024   Code   RO         6867    i.sd_transfer_stop  sdio_sdcard.o
    0x0800a98c   0x0800a98c   0x00000034   Code   RO         6771    i.sd_write_disk     sd_conf.o
    0x0800a9c0   0x0800a9c0   0x0000001c   Code   RO         4512    i.sdio_bus_mode_set  gd32f4xx_sdio.o
    0x0800a9dc   0x0800a9dc   0x00000034   Code   RO         4519    i.sdio_clock_config  gd32f4xx_sdio.o
    0x0800aa10   0x0800aa10   0x00000014   Code   RO         4521    i.sdio_clock_enable  gd32f4xx_sdio.o
    0x0800aa24   0x0800aa24   0x0000000c   Code   RO         4522    i.sdio_command_index_get  gd32f4xx_sdio.o
    0x0800aa30   0x0800aa30   0x00000038   Code   RO         4523    i.sdio_command_response_config  gd32f4xx_sdio.o
    0x0800aa68   0x0800aa68   0x00000014   Code   RO         4525    i.sdio_csm_enable   gd32f4xx_sdio.o
    0x0800aa7c   0x0800aa7c   0x0000003c   Code   RO         4526    i.sdio_data_config  gd32f4xx_sdio.o
    0x0800aab8   0x0800aab8   0x0000000c   Code   RO         4528    i.sdio_data_read    gd32f4xx_sdio.o
    0x0800aac4   0x0800aac4   0x0000001c   Code   RO         4529    i.sdio_data_transfer_config  gd32f4xx_sdio.o
    0x0800aae0   0x0800aae0   0x0000000c   Code   RO         4530    i.sdio_data_write   gd32f4xx_sdio.o
    0x0800aaec   0x0800aaec   0x00000014   Code   RO         4531    i.sdio_deinit       gd32f4xx_sdio.o
    0x0800ab00   0x0800ab00   0x00000014   Code   RO         4532    i.sdio_dma_disable  gd32f4xx_sdio.o
    0x0800ab14   0x0800ab14   0x00000014   Code   RO         4533    i.sdio_dma_enable   gd32f4xx_sdio.o
    0x0800ab28   0x0800ab28   0x00000014   Code   RO         4534    i.sdio_dsm_disable  gd32f4xx_sdio.o
    0x0800ab3c   0x0800ab3c   0x00000014   Code   RO         4535    i.sdio_dsm_enable   gd32f4xx_sdio.o
    0x0800ab50   0x0800ab50   0x0000000c   Code   RO         4537    i.sdio_flag_clear   gd32f4xx_sdio.o
    0x0800ab5c   0x0800ab5c   0x00000014   Code   RO         4538    i.sdio_flag_get     gd32f4xx_sdio.o
    0x0800ab70   0x0800ab70   0x00000014   Code   RO         4539    i.sdio_hardware_clock_disable  gd32f4xx_sdio.o
    0x0800ab84   0x0800ab84   0x00000010   Code   RO         4541    i.sdio_interrupt_disable  gd32f4xx_sdio.o
    0x0800ab94   0x0800ab94   0x00000010   Code   RO         4542    i.sdio_interrupt_enable  gd32f4xx_sdio.o
    0x0800aba4   0x0800aba4   0x0000000c   Code   RO         4543    i.sdio_interrupt_flag_clear  gd32f4xx_sdio.o
    0x0800abb0   0x0800abb0   0x00000014   Code   RO         4544    i.sdio_interrupt_flag_get  gd32f4xx_sdio.o
    0x0800abc4   0x0800abc4   0x0000000c   Code   RO         4547    i.sdio_power_state_get  gd32f4xx_sdio.o
    0x0800abd0   0x0800abd0   0x0000000c   Code   RO         4548    i.sdio_power_state_set  gd32f4xx_sdio.o
    0x0800abdc   0x0800abdc   0x0000003c   Code   RO         4552    i.sdio_response_get  gd32f4xx_sdio.o
    0x0800ac18   0x0800ac18   0x000000cc   Code   RO         6773    i.sdio_sd_init      sd_conf.o
    0x0800ace4   0x0800ace4   0x0000001c   Code   RO         4557    i.sdio_wait_type_set  gd32f4xx_sdio.o
    0x0800ad00   0x0800ad00   0x00000180   Code   RO           25    i.set_limit         main.o
    0x0800ae80   0x0800ae80   0x00000180   Code   RO           26    i.set_ratio         main.o
    0x0800b000   0x0800b000   0x000000bc   Code   RO           27    i.set_rtc_time      main.o
    0x0800b0bc   0x0800b0bc   0x000000e4   Code   RO         7056    i.spi1_init         spi.o
    0x0800b1a0   0x0800b1a0   0x00000038   Code   RO         7057    i.spi1_read_write_byte  spi.o
    0x0800b1d8   0x0800b1d8   0x00000030   Code   RO         7058    i.spi1_set_speed    spi.o
    0x0800b208   0x0800b208   0x0000000a   Code   RO         4819    i.spi_disable       gd32f4xx_spi.o
    0x0800b212   0x0800b212   0x0000000a   Code   RO         4822    i.spi_enable        gd32f4xx_spi.o
    0x0800b21c   0x0800b21c   0x00000008   Code   RO         4824    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x0800b224   0x0800b224   0x00000004   Code   RO         4825    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x0800b228   0x0800b228   0x000000ac   Code   RO         4826    i.spi_i2s_deinit    gd32f4xx_spi.o
    0x0800b2d4   0x0800b2d4   0x00000010   Code   RO         4827    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x0800b2e4   0x0800b2e4   0x00000032   Code   RO         4832    i.spi_init          gd32f4xx_spi.o
    0x0800b316   0x0800b316   0x00000012   Code   RO         4843    i.spi_struct_para_init  gd32f4xx_spi.o
    0x0800b328   0x0800b328   0x00000024   Code   RO         7258    i.st_clust          ff.o
    0x0800b34c   0x0800b34c   0x00000018   Code   RO         7259    i.st_dword          ff.o
    0x0800b364   0x0800b364   0x0000005c   Code   RO         7260    i.st_qword          ff.o
    0x0800b3c0   0x0800b3c0   0x0000000c   Code   RO         7261    i.st_word           ff.o
    0x0800b3cc   0x0800b3cc   0x0000010c   Code   RO           28    i.start_sampling    main.o
    0x0800b4d8   0x0800b4d8   0x000000e4   Code   RO           29    i.stop_sampling     main.o
    0x0800b5bc   0x0800b5bc   0x00000144   Code   RO           30    i.store_hide_data   main.o
    0x0800b700   0x0800b700   0x00000160   Code   RO           31    i.store_log_entry   main.o
    0x0800b860   0x0800b860   0x00000160   Code   RO           32    i.store_overlimit_data  main.o
    0x0800b9c0   0x0800b9c0   0x00000164   Code   RO           33    i.store_sample_data  main.o
    0x0800bb24   0x0800bb24   0x00000072   Code   RO         7262    i.store_xdir        ff.o
    0x0800bb96   0x0800bb96   0x00000020   Code   RO         7263    i.sum_sfn           ff.o
    0x0800bbb6   0x0800bbb6   0x00000002   PAD
    0x0800bbb8   0x0800bbb8   0x0000008c   Code   RO         7264    i.sync_fs           ff.o
    0x0800bc44   0x0800bc44   0x0000004a   Code   RO         7265    i.sync_window       ff.o
    0x0800bc8e   0x0800bc8e   0x00000002   PAD
    0x0800bc90   0x0800bc90   0x00000108   Code   RO          430    i.system_clock_240m_25m_hxtal  system_gd32f4xx.o
    0x0800bd98   0x0800bd98   0x00000008   Code   RO          431    i.system_clock_config  system_gd32f4xx.o
    0x0800bda0   0x0800bda0   0x00000160   Code   RO           34    i.system_self_test  main.o
    0x0800bf00   0x0800bf00   0x0000004c   Code   RO         7266    i.tchar2uni         ff.o
    0x0800bf4c   0x0800bf4c   0x00000184   Code   RO         5162    i.timer_deinit      gd32f4xx_timer.o
    0x0800c0d0   0x0800c0d0   0x0000000a   Code   RO         5163    i.timer_disable     gd32f4xx_timer.o
    0x0800c0da   0x0800c0da   0x0000000a   Code   RO         5167    i.timer_enable      gd32f4xx_timer.o
    0x0800c0e4   0x0800c0e4   0x00000098   Code   RO         5177    i.timer_init        gd32f4xx_timer.o
    0x0800c17c   0x0800c17c   0x00000008   Code   RO         5184    i.timer_interrupt_enable  gd32f4xx_timer.o
    0x0800c184   0x0800c184   0x00000006   Code   RO         5185    i.timer_interrupt_flag_clear  gd32f4xx_timer.o
    0x0800c18a   0x0800c18a   0x00000018   Code   RO         5186    i.timer_interrupt_flag_get  gd32f4xx_timer.o
    0x0800c1a2   0x0800c1a2   0x00000016   Code   RO         5197    i.timer_struct_para_init  gd32f4xx_timer.o
    0x0800c1b8   0x0800c1b8   0x0000005c   Code   RO         7094    i.timerx_int_init   timer.o
    0x0800c214   0x0800c214   0x000000c0   Code   RO           35    i.update_oled_time  main.o
    0x0800c2d4   0x0800c2d4   0x000000e8   Code   RO         5827    i.usart_baudrate_set  gd32f4xx_usart.o
    0x0800c3bc   0x0800c3bc   0x0000000a   Code   RO         5831    i.usart_data_receive  gd32f4xx_usart.o
    0x0800c3c6   0x0800c3c6   0x00000008   Code   RO         5832    i.usart_data_transmit  gd32f4xx_usart.o
    0x0800c3ce   0x0800c3ce   0x00000002   PAD
    0x0800c3d0   0x0800c3d0   0x000000dc   Code   RO         5833    i.usart_deinit      gd32f4xx_usart.o
    0x0800c4ac   0x0800c4ac   0x0000000a   Code   RO         5837    i.usart_enable      gd32f4xx_usart.o
    0x0800c4b6   0x0800c4b6   0x0000001e   Code   RO         5839    i.usart_flag_get    gd32f4xx_usart.o
    0x0800c4d4   0x0800c4d4   0x000000c8   Code   RO          578    i.usart_init        usart.o
    0x0800c59c   0x0800c59c   0x0000001a   Code   RO         5847    i.usart_interrupt_enable  gd32f4xx_usart.o
    0x0800c5b6   0x0800c5b6   0x00000038   Code   RO         5849    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x0800c5ee   0x0800c5ee   0x00000010   Code   RO         5862    i.usart_parity_config  gd32f4xx_usart.o
    0x0800c5fe   0x0800c5fe   0x00000010   Code   RO         5864    i.usart_receive_config  gd32f4xx_usart.o
    0x0800c60e   0x0800c60e   0x00000010   Code   RO         5875    i.usart_stop_bit_set  gd32f4xx_usart.o
    0x0800c61e   0x0800c61e   0x00000010   Code   RO         5879    i.usart_transmit_config  gd32f4xx_usart.o
    0x0800c62e   0x0800c62e   0x00000010   Code   RO         5880    i.usart_word_length_set  gd32f4xx_usart.o
    0x0800c63e   0x0800c63e   0x0000003c   Code   RO         7267    i.validate          ff.o
    0x0800c67a   0x0800c67a   0x0000003a   Code   RO         7268    i.xdir_sum          ff.o
    0x0800c6b4   0x0800c6b4   0x0000004c   Code   RO         7269    i.xname_sum         ff.o
    0x0800c700   0x0800c700   0x0000002c   Code   RO         8278    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800c72c   0x0800c72c   0x0000002c   Code   RO         8387    locale$$code        c_w.l(lc_ctype_c.o)
    0x0800c758   0x0800c758   0x00000062   Code   RO         8152    x$fpl$d2f           fz_wm.l(d2f.o)
    0x0800c7ba   0x0800c7ba   0x00000002   PAD
    0x0800c7bc   0x0800c7bc   0x00000010   Code   RO         8466    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x0800c7cc   0x0800c7cc   0x00000018   Code   RO         8395    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x0800c7e4   0x0800c7e4   0x00000078   Code   RO         8370    x$fpl$deqf          fz_wm.l(deqf.o)
    0x0800c85c   0x0800c85c   0x00000078   Code   RO         8397    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0800c8d4   0x0800c8d4   0x00000154   Code   RO         8154    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800ca28   0x0800ca28   0x0000009c   Code   RO         8204    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800cac4   0x0800cac4   0x0000000c   Code   RO         8206    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800cad0   0x0800cad0   0x0000006c   Code   RO         8372    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x0800cb3c   0x0800cb3c   0x00000056   Code   RO         8156    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800cb92   0x0800cb92   0x0000008c   Code   RO         8208    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800cc1e   0x0800cc1e   0x0000000a   Code   RO         8399    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800cc28   0x0800cc28   0x0000000a   Code   RO         8210    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800cc32   0x0800cc32   0x00000006   Code   RO         8284    x$fpl$ieeestatus    fz_wm.l(istatus.o)
    0x0800cc38   0x0800cc38   0x00000004   Code   RO         8158    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800cc3c   0x0800cc3c   0x00000064   Code   RO         8506    x$fpl$retnan        fz_wm.l(retnan.o)
    0x0800cca0   0x0800cca0   0x0000005c   Code   RO         8432    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x0800ccfc   0x0800ccfc   0x00000004   Code   RO         8160    x$fpl$scanf1        fz_wm.l(scanf1.o)
    0x0800cd00   0x0800cd00   0x00000008   Code   RO         8286    x$fpl$scanf2        fz_wm.l(scanf2.o)
    0x0800cd08   0x0800cd08   0x00000030   Code   RO         8508    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x0800cd38   0x0800cd38   0x00000000   Code   RO         8212    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800cd38   0x0800cd38   0x000018e0   Data   RO         6346    .constdata          oled.o
    0x0800e618   0x0800e618   0x00000058   Data   RO         7271    .constdata          ff.o
    0x0800e670   0x0800e670   0x0002abb6   Data   RO         7682    .constdata          ffunicode.o
    0x08039226   0x08039226   0x00000002   PAD
    0x08039228   0x08039228   0x00000018   Data   RO         7978    .constdata          malloc.o
    0x08039240   0x08039240   0x00000028   Data   RO         8071    .constdata          c_w.l(_printf_hex_int.o)
    0x08039268   0x08039268   0x00000011   Data   RO         8106    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08039279   0x08039279   0x0000000c   Data   RO         8203    .constdata          c_w.l(_monlen.o)
    0x08039285   0x08039285   0x00000003   PAD
    0x08039288   0x08039288   0x00000094   Data   RO         8249    .constdata          c_w.l(bigflt0.o)
    0x0803931c   0x0803931c   0x00000020   Data   RO         8510    Region$$Table       anon$$obj.o
    0x0803933c   0x0803933c   0x00000008   Data   RO         8280    c$$dinf             fz_wm.l(fpconst.o)
    0x08039344   0x08039344   0x00000008   Data   RO         8283    c$$dmax             fz_wm.l(fpconst.o)
    0x0803934c   0x0803934c   0x0000001c   Data   RO         8277    locale$$data        c_w.l(lc_numeric_c.o)
    0x08039368   0x08039368   0x00000110   Data   RO         8386    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08039478, Size: 0x0001c260, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08039478   0x0000003f   Data   RW           37    .data               main.o
    0x2000003f   0x080394b7   0x00000001   PAD
    0x20000040   0x080394b8   0x00000004   Data   RW          477    .data               delay.o
    0x20000044   0x080394bc   0x00000006   Data   RW          580    .data               usart.o
    0x2000004a   0x080394c2   0x00000001   Data   RW         6435    .data               key.o
    0x2000004b   0x080394c3   0x00000001   PAD
    0x2000004c   0x080394c4   0x00000002   Data   RW         6578    .data               norflash.o
    0x2000004e   0x080394c6   0x00000002   PAD
    0x20000050   0x080394c8   0x00000024   Data   RW         6869    .data               sdio_sdcard.o
    0x20000074   0x080394ec   0x0000000a   Data   RW         7272    .data               ff.o
    0x2000007e   0x080394f6   0x00000002   PAD
    0x20000080   0x080394f8   0x0000001c   Data   RW         7979    .data               malloc.o
    0x2000009c   0x08039514   0x00000004   PAD
    0x200000a0        -       0x00000992   Zero   RW           36    .bss                main.o
    0x20000a32        -       0x000000c8   Zero   RW          579    .bss                usart.o
    0x20000afa        -       0x00000200   Zero   RW         6345    .bss                oled.o
    0x20000cfa        -       0x00001000   Zero   RW         6577    .bss                norflash.o
    0x20001cfa   0x08039514   0x00000002   PAD
    0x20001cfc        -       0x00000014   Zero   RW         6687    .bss                rtc.o
    0x20001d10        -       0x00000048   Zero   RW         6774    .bss                sd_conf.o
    0x20001d58        -       0x00000020   Zero   RW         6868    .bss                sdio_sdcard.o
    0x20001d78   0x08039514   0x00000008   PAD
    0x20001d80        -       0x00019c80   Zero   RW         7977    .bss                malloc.o
    0x2001ba00        -       0x00000060   Zero   RW         8303    .bss                c_w.l(libspace.o)
    0x2001ba60        -       0x00000400   Zero   RW            2    HEAP                startup_gd32f450_470.o
    0x2001be60        -       0x00000400   Zero   RW            1    STACK               startup_gd32f450_470.o



  Load Region LR$$.ARM.__AT_0x10000000 (Base: 0x10000000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x10000000 (Exec base: 0x10000000, Load base: 0x10000000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x10000000        -       0x0000f000   Zero   RW         7975    .ARM.__AT_0x10000000  malloc.o



  Load Region LR$$.ARM.__AT_0x1000F000 (Base: 0x1000f000, Size: 0x00000000, Max: 0x00000780, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x1000F000 (Exec base: 0x1000f000, Load base: 0x1000f000, Size: 0x00000780, Max: 0x00000780, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x1000f000        -       0x00000780   Zero   RW         7976    .ARM.__AT_0x1000F000  malloc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       240         16          0          0          0       1849   adc.o
       134         10          0          4          0       1983   delay.o
       450          6          0          0          0       5414   diskio.o
     11740        110         88         10          0      75924   ff.o
        32          0          0          0          0       1697   ffsystem.o
       396         28     175030          0          0       5780   ffunicode.o
       652         16          0          0          0      10172   gd32f4xx_adc.o
       826          4          0          0          0       6287   gd32f4xx_dma.o
        12          6          0          0          0        590   gd32f4xx_exti.o
       268          0          0          0          0       4540   gd32f4xx_gpio.o
        34          0          0          0          0       4226   gd32f4xx_it.o
       216         20          0          0          0       1532   gd32f4xx_misc.o
        20          6          0          0          0        570   gd32f4xx_pmu.o
       844         60          0          0          0       6545   gd32f4xx_rcu.o
       544         32          0          0          0       5538   gd32f4xx_rtc.o
       628        136          0          0          0      16166   gd32f4xx_sdio.o
       288         10          0          0          0       5744   gd32f4xx_spi.o
       620         44          0          0          0       5825   gd32f4xx_timer.o
       672         18          0          0          0       9101   gd32f4xx_usart.o
       480         28          0          1          0       1334   key.o
       288          8          0          0          0        603   led.o
      6344       2214          0         63       2450     125456   main.o
       484         54         24         28     168960       6515   malloc.o
       418         22          0          0          0       3213   myiic.o
      1052         62          0          2       4096       8720   norflash.o
       898         28       6368          0        512       6300   oled.o
       642         50          0          0         20       6660   rtc.o
       316         70          0          0         72       3234   sd_conf.o
      7946        386          0         36         32      34020   sdio_sdcard.o
       332         20          0          0          0       1820   spi.o
        64         26        428          0       2048        988   startup_gd32f450_470.o
       484         32          0          0          0       2621   system_gd32f4xx.o
        92          6          0          0          0        674   timer.o
       376         30          0          6        200       4357   usart.o

    ----------------------------------------------------------------------
     38872       <USER>     <GROUP>        156     178404     375998   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          4          2          6         14          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         0          0         12          0          0          0   _monlen.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        10          0          0          0          0         68   _sputc.o
        16          0          0          0          0         68   aeabi_memset.o
       228          4        148          0          0         96   bigflt0.o
      2152        136          0          0          0        960   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        88          0          0          0          0         76   memcmp.o
       428         10          0          0          0        116   mktime.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
      1272         16          0          0          0        168   scanf_fp.o
       800         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
        24          0          0          0          0         68   strcat.o
        20          0          0          0          0         68   strchr.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
       150          0          0          0          0         80   strncmp.o
        36          0          0          0          0         80   strstr.o
       164         14          0          0          0        120   strtod.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        98          4          0          0          0        140   d2f.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       120          4          0          0          0        140   deqf.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
         0          0         16          0          0          0   fpconst.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         6          0          0          0          0        116   istatus.o
         4          0          0          0          0        116   printf1.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
         4          0          0          0          0        116   scanf1.o
         8          0          0          0          0        132   scanf2.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
        56         12          0          0          0        132   atof.o
        64         16          0          0          0        248   dunder.o
        48          0          0          0          0        124   fpclassify.o
       140         22          0          0          0        132   frexp.o
       228          8          0          0          0        308   ldexp.o
       266         16          0          0          0        308   narrow.o

    ----------------------------------------------------------------------
     13236        <USER>        <GROUP>          0         96       9696   Library Totals
        14          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

     10918        334        517          0         96       5932   c_w.l
      1502         40         16          0          0       2512   fz_wm.l
       802         74          0          0          0       1252   m_wm.l

    ----------------------------------------------------------------------
     13236        <USER>        <GROUP>          0         96       9696   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     52108       4010     182508        156     178500     350314   Grand Totals
     52108       4010     182508        156     178500     350314   ELF Image Totals
     52108       4010     182508        156          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)               234616 ( 229.12kB)
    Total RW  Size (RW Data + ZI Data)            178656 ( 174.47kB)
    Total ROM Size (Code + RO Data + RW Data)     234772 ( 229.27kB)

==============================================================================

